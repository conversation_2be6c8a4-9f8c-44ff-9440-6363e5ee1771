#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查找天降神兵技能相关代码
"""

def find_heaven_sword_code():
    """查找天降神兵技能相关代码"""
    files_to_search = ['combat_system.py', 'config.py', 'main.py']
    
    for filename in files_to_search:
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"\n{'='*50}")
            print(f"搜索文件: {filename}")
            print('='*50)
            
            keywords = ['天剑', '天降神兵', 'heaven', 'sword', 'HeavenSword', '天剑术']
            
            for keyword in keywords:
                found_lines = []
                for i, line in enumerate(lines, 1):
                    if keyword in line:
                        found_lines.append((i, line.strip()))
                
                if found_lines:
                    print(f"\n关键词 '{keyword}' 相关代码:")
                    for line_num, line_content in found_lines:
                        print(f"  第{line_num}行: {line_content}")
                        
                        # 如果是类定义或方法定义，显示更多上下文
                        if 'class ' in line_content or 'def ' in line_content:
                            start = max(0, i-2)
                            end = min(len(lines), i+15)
                            print(f"    上下文 (第{start+1}-{end}行):")
                            for j in range(start, end):
                                marker = ">>> " if j == i-1 else "    "
                                print(f"    {marker}{j+1:4d}: {lines[j].rstrip()}")
                            print()
        
        except FileNotFoundError:
            print(f"文件 {filename} 不存在")
        except Exception as e:
            print(f"搜索 {filename} 失败: {e}")

if __name__ == "__main__":
    find_heaven_sword_code()
