#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查找事件处理方法
"""

def find_handle_events():
    """查找事件处理方法"""
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print("搜索事件处理方法...")
        
        for i, line in enumerate(lines, 1):
            if 'def handle_events' in line or 'def run' in line or 'event' in line:
                print(f"第{i}行: {line.strip()}")
                
                # 显示周围代码
                if 'def ' in line:
                    start = max(0, i-1)
                    end = min(len(lines), i+20)
                    print(f"  周围代码 (第{start+1}-{end}行):")
                    for j in range(start, end):
                        marker = ">>> " if j == i-1 else "    "
                        print(f"  {marker}{j+1:4d}: {lines[j].rstrip()}")
                    print()
        
    except Exception as e:
        print(f"搜索失败: {e}")

if __name__ == "__main__":
    find_handle_events()
