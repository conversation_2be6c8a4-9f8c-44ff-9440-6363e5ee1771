# combat_system.py
import pygame
import random
import math
import os
import io
from config import BASE_SKILLS, COMBO_SKILLS, ELEMENT_COLORS, IMAGE_PATHS, SCREEN_WIDTH, SCREEN_HEIGHT
from particle import ParticleSystem
try:
    from PIL import Image, ImageSequence
except ImportError:
    print("警告: PIL库未安装，GIF动画功能将不可用")

class VisualEffect(pygame.sprite.Sprite):
    """视觉效果类"""
    def __init__(self, position, image_path, duration, follow_target=False, scale=1.0, alpha=255, is_animation=False):
        super().__init__()
        self.position = pygame.math.Vector2(position)
        self.image_path = image_path
        self.duration = duration
        self.follow_target = follow_target
        self.scale = scale
        self.alpha = alpha
        self.is_animation = is_animation
        self.is_character_effect = False  # 默认不是角色效果

        # 创建时间
        self.creation_time = pygame.time.get_ticks()

        # 加载图像
        self.frames = []
        self.frame_durations = []
        self.current_frame = 0
        self.last_frame_time = self.creation_time

        if self.is_animation and self.image_path and self.image_path.lower().endswith('.gif'):
            # 加载GIF动画
            self.frames, self.frame_durations = load_gif_frames(self.image_path)
            if self.frames:
                self.image = self.frames[0].copy()
                # 应用缩放
                if self.scale != 1.0:
                    new_size = (int(self.image.get_width() * self.scale),
                               int(self.image.get_height() * self.scale))
                    self.image = pygame.transform.scale(self.image, new_size)
                # 应用透明度
                if self.alpha != 255:
                    self.image.set_alpha(self.alpha)
            else:
                # 创建默认图像
                self.image = pygame.Surface((50, 50), pygame.SRCALPHA)
                pygame.draw.circle(self.image, (255, 255, 255, self.alpha), (25, 25), 25)
        elif self.image_path:
            # 加载静态图像
            try:
                self.image = pygame.image.load(self.image_path).convert_alpha()
                # 应用缩放
                if self.scale != 1.0:
                    new_size = (int(self.image.get_width() * self.scale),
                               int(self.image.get_height() * self.scale))
                    self.image = pygame.transform.scale(self.image, new_size)
                # 应用透明度
                if self.alpha != 255:
                    self.image.set_alpha(self.alpha)
            except Exception as e:
                print(f"加载图像失败: {self.image_path}, 错误: {e}")
                # 创建默认图像
                self.image = pygame.Surface((50, 50), pygame.SRCALPHA)
                pygame.draw.circle(self.image, (255, 255, 255, self.alpha), (25, 25), 25)
        else:
            # 创建默认图像
            self.image = pygame.Surface((50, 50), pygame.SRCALPHA)
            pygame.draw.circle(self.image, (255, 255, 255, self.alpha), (25, 25), 25)

        # 设置矩形
        self.rect = self.image.get_rect(center=position)

    def update(self, dt=16):
        """更新效果"""
        # 检查生命周期
        current_time = pygame.time.get_ticks()
        if current_time - self.creation_time > self.duration:
            self.kill()
            return

        # 更新动画帧
        if self.is_animation and self.frames:
            if current_time - self.last_frame_time > self.frame_durations[self.current_frame]:
                self.last_frame_time = current_time
                self.current_frame = (self.current_frame + 1) % len(self.frames)
                self.image = self.frames[self.current_frame].copy()

                # 应用缩放
                if self.scale != 1.0:
                    new_size = (int(self.image.get_width() * self.scale),
                               int(self.image.get_height() * self.scale))
                    self.image = pygame.transform.scale(self.image, new_size)

                # 应用透明度
                if self.alpha != 255:
                    self.image.set_alpha(self.alpha)

                # 更新矩形位置
                old_center = self.rect.center
                self.rect = self.image.get_rect(center=old_center)

        # 如果需要跟随目标，更新位置
        if self.follow_target and hasattr(self, 'target') and self.target:
            if hasattr(self.target, 'rect'):
                self.rect.center = self.target.rect.center
            elif hasattr(self.target, 'position'):
                self.rect.center = self.target.position

def load_image_with_transparency(image_path):
    """加载图像并将黑色设为透明"""
    try:
        image = pygame.image.load(image_path).convert_alpha()
        # 将黑色设为透明色（隐藏黑色背景）
        image.set_colorkey((0, 0, 0))  # 黑色透明
        print(f"加载图像成功: {image_path}, 黑色已设为透明")
        return image
    except Exception as e:
        print(f"加载图像失败: {image_path}, 错误: {e}")
        return None

def load_gif_frames(gif_path):
    """加载GIF动画的所有帧，并将黑色设为透明"""
    try:
        frames = []
        durations = []

        # 使用PIL打开GIF
        with Image.open(gif_path) as img:
            for frame in ImageSequence.Iterator(img):
                # 转换为pygame表面
                frame_surface = pygame.image.fromstring(
                    frame.convert("RGBA").tobytes(), frame.size, "RGBA")

                # 将黑色设为透明色（隐藏黑色背景）
                frame_surface.set_colorkey((0, 0, 0))  # 黑色透明

                frames.append(frame_surface)

                # 获取帧持续时间
                durations.append(frame.info.get('duration', 100))  # 默认100ms

        print(f"加载GIF动画成功: {gif_path}, 帧数: {len(frames)}, 黑色已设为透明")
        return frames, durations
    except Exception as e:
        print(f"加载GIF动画失败: {e}")
        return [], []

def safe_game_access(obj):
    """安全地检查游戏对象和关卡系统是否存在"""
    if not hasattr(obj, 'game') or not obj.game:
        return False
    if not hasattr(obj.game, 'level_system'):
        return False
    return True

class SkillEffect:
    def __init__(self, image_path, position, duration=500, is_animation=False, frames=1,
                 frame_duration=100, fade_in=False, fade_out=False, flip_x=False):
        self.position = position
        self.creation_time = pygame.time.get_ticks()
        self.duration = duration
        self.is_animation = is_animation
        self.current_frame = 0
        self.frame_duration = frame_duration
        self.last_frame_time = self.creation_time
        self.fade_in = fade_in
        self.fade_out = fade_out
        self.alpha = 0 if fade_in else 255
        self.flip_x = flip_x  # 新增：是否水平翻转

        # 加载图像
        try:
            # 检查是否为GIF动画
            if is_animation and image_path.lower().endswith('.gif'):
                # 使用Pillow加载GIF帧
                self.frames, frame_durations = load_gif_frames(image_path)

                if self.frames:
                    # 如果成功加载了帧，使用它们
                    self.original_image = self.frames[0]
                    self.image = self.original_image.copy()

                    # 如果有帧持续时间信息，使用它
                    if frame_durations:
                        # 计算平均帧持续时间
                        self.frame_duration = sum(frame_durations) / len(frame_durations)
                else:
                    # 如果加载失败，创建默认帧
                    raise Exception("未能加载GIF动画帧")
            else:
                # 静态图像或非GIF动画
                if is_animation:
                    # 非GIF的动画处理 - 假设有多个命名的帧文件
                    self.frames = []
                    base_path = image_path.replace('.gif', '')
                    for i in range(frames):
                        frame_path = f"{base_path}_{i}.png"
                        try:
                            frame = pygame.image.load(frame_path).convert_alpha()
                            self.frames.append(frame)
                        except Exception as e:
                            print(f"加载动画帧 {frame_path} 失败: {e}")

                    if not self.frames:  # 如果没有成功加载任何帧
                        raise Exception("未能加载任何动画帧")

                    self.original_image = self.frames[0]
                    self.image = self.original_image.copy()
                else:
                    # 静态图像
                    self.original_image = pygame.image.load(image_path).convert_alpha()
                    self.image = self.original_image.copy()

        except Exception as e:
            print(f"加载技能效果图像失败 {image_path}: {e}")
            # 创建一个默认图像
            self.original_image = pygame.Surface((50, 50), pygame.SRCALPHA)
            pygame.draw.circle(self.original_image, (255, 255, 255, 150), (25, 25), 25)
            self.image = self.original_image.copy()

        # 应用水平翻转（如果需要）
        if self.flip_x:
            if hasattr(self, 'frames') and self.frames:
                # 翻转所有帧
                self.frames = [pygame.transform.flip(frame, True, False) for frame in self.frames]
                if self.frames:
                    self.original_image = self.frames[0]
                    self.image = self.original_image.copy()
            else:
                # 翻转单个图像
                self.original_image = pygame.transform.flip(self.original_image, True, False)
                self.image = self.original_image.copy()

        self.rect = self.image.get_rect(center=position)

    def update(self):
        current_time = pygame.time.get_ticks()
        elapsed_time = current_time - self.creation_time

        # 检查是否过期
        if elapsed_time > self.duration:
            return False

        # 处理渐变效果
        if self.fade_in or self.fade_out:
            # 计算当前应该的透明度
            if self.fade_in and elapsed_time < self.duration / 3:  # 前1/3时间用于淡入
                self.alpha = int(255 * (elapsed_time / (self.duration / 3)))
            elif self.fade_out and elapsed_time > self.duration * 2 / 3:  # 后1/3时间用于淡出
                self.alpha = int(255 * (1 - (elapsed_time - self.duration * 2 / 3) / (self.duration / 3)))
            else:
                self.alpha = 255

            # 应用透明度
            if self.is_animation and hasattr(self, 'frames') and self.frames:
                # 对于动画，需要对当前帧应用透明度
                current_frame = self.frames[int(self.current_frame)]
                self.image = current_frame.copy()
                self.image.set_alpha(self.alpha)
            else:
                # 对于静态图像，直接应用透明度
                self.image = self.original_image.copy()
                self.image.set_alpha(self.alpha)

        # 更新动画帧
        if self.is_animation and hasattr(self, 'frames') and len(self.frames) > 1:
            if current_time - self.last_frame_time > self.frame_duration:
                self.current_frame = (self.current_frame + 1) % len(self.frames)
                if not self.fade_in and not self.fade_out:  # 如果不需要渐变效果，直接更新图像
                    self.image = self.frames[int(self.current_frame)]
                self.last_frame_time = current_time

        return True

    def draw(self, surface):
        surface.blit(self.image, self.rect)

class Projectile(pygame.sprite.Sprite):
    def __init__(self, start_pos, velocity, damage, lifetime=2000, image=None, effect_type=None):
        super().__init__()
        self.position = pygame.math.Vector2(start_pos)
        self.velocity = pygame.math.Vector2(velocity)
        self.damage = damage
        self.creation_time = pygame.time.get_ticks()
        self.lifetime = lifetime
        self.effect_type = effect_type

        # 设置图像
        if image:
            self.image = image
        else:
            self.image = pygame.Surface((10, 10), pygame.SRCALPHA)
            pygame.draw.circle(self.image, (255, 255, 255), (5, 5), 5)

        self.rect = self.image.get_rect(center=start_pos)

    def update(self, walls, enemies, player):
        # 更新位置
        self.position += self.velocity
        self.rect.center = (int(self.position.x), int(self.position.y))

        # 检查生命周期
        if pygame.time.get_ticks() - self.creation_time > self.lifetime:
            self.kill()
            return

        # 检查墙体碰撞
        for wall in walls:
            if self.rect.colliderect(wall):
                self.on_wall_collision()
                return

        # 检查敌人碰撞
        for enemy in enemies:
            if self.rect.colliderect(enemy.rect):
                self.on_enemy_collision(enemy)
                return

    def on_wall_collision(self):
        self.kill()

    def on_enemy_collision(self, enemy):
        enemy.hp -= self.damage
        self.kill()

class WaterWave(pygame.sprite.Sprite):
    def __init__(self, center_pos, damage, game=None):
        super().__init__()
        self.position = pygame.math.Vector2(center_pos)
        self.damage = damage
        self.creation_time = pygame.time.get_ticks()
        self.lifetime = 1500  # 生命周期1.5秒
        self.game = game

        # 加载水波GIF图像
        try:
            self.frames, self.frame_durations = load_gif_frames(IMAGE_PATHS["water_effect"])
            if self.frames:
                self.image = self.frames[0]
                self.current_frame = 0
                self.last_frame_time = self.creation_time
                self.is_gif = True
                print(f"成功加载水波GIF: {IMAGE_PATHS['water_effect']}, 帧数: {len(self.frames)}")
            else:
                raise Exception("未能加载GIF帧")
        except Exception as e:
            print(f"加载水波GIF失败: {e}")
            # 创建默认图像
            self.image = pygame.Surface((100, 100), pygame.SRCALPHA)
            pygame.draw.circle(self.image, (0, 150, 255, 180), (50, 50), 50)
            self.is_gif = False

        self.rect = self.image.get_rect(center=center_pos)

        # 添加伤害间隔和已伤害敌人列表
        self.damage_interval = 300  # 每0.3秒造成一次伤害
        self.last_damage_time = self.creation_time
        self.damaged_enemies = set()  # 记录已经伤害过的敌人

        # 添加击退效果
        self.knockback_distance = 30

        # 攻击范围（基于图像大小）
        self.attack_radius = max(self.image.get_width(), self.image.get_height()) // 2

        print(f"创建水波攻击: 位置={center_pos}, 伤害={damage}, 攻击范围={self.attack_radius}")

    def update(self, dt=16):
        current_time = pygame.time.get_ticks()

        # 更新GIF动画
        if self.is_gif and hasattr(self, 'frames') and self.frames:
            if current_time - self.last_frame_time > 100:  # 每100毫秒更新一帧
                self.current_frame = (self.current_frame + 1) % len(self.frames)
                self.image = self.frames[self.current_frame]
                self.last_frame_time = current_time

                # 更新矩形位置
                old_center = self.rect.center
                self.rect = self.image.get_rect(center=old_center)

                # 更新攻击范围
                self.attack_radius = max(self.image.get_width(), self.image.get_height()) // 2

        # 检查生命周期
        if current_time - self.creation_time > self.lifetime:
            self.kill()
            return

        # 检查敌人碰撞
        if hasattr(self, 'game') and self.game and hasattr(self.game, 'level_system'):
            if current_time - self.last_damage_time > self.damage_interval:
                self.last_damage_time = current_time

                # 每次伤害间隔重置已伤害敌人集合
                self.damaged_enemies = set()

                for enemy in self.game.level_system.enemies:
                    # 计算与敌人的距离
                    enemy_pos = pygame.math.Vector2(enemy.rect.center)
                    distance = self.position.distance_to(enemy_pos)

                    # 如果在范围内且未被伤害过，造成伤害
                    if distance <= self.attack_radius and enemy not in self.damaged_enemies:
                        # 造成伤害
                        old_hp = enemy.hp
                        enemy.hp -= self.damage
                        self.damaged_enemies.add(enemy)
                        print(f"水波击中敌人! 造成 {self.damage} 点伤害，敌人HP: {old_hp} -> {enemy.hp}")

                        # 添加受伤特效
                        if hasattr(self.game, 'combat_system'):
                            self.game.combat_system.add_damage_effect(enemy.rect.center, self.damage)

                        # 计算击退方向（从水波中心向敌人方向）
                        knockback_dir = pygame.math.Vector2(enemy.rect.center) - self.position
                        if knockback_dir.length() > 0:
                            knockback_dir.normalize_ip()

                            # 应用击退效果
                            new_x = enemy.rect.x + knockback_dir.x * self.knockback_distance
                            new_y = enemy.rect.y + knockback_dir.y * self.knockback_distance

                            # 检查新位置是否与墙体碰撞
                            temp_rect = enemy.rect.copy()
                            temp_rect.x = new_x
                            temp_rect.y = new_y

                            wall_collision = False
                            for wall in self.game.level_system.walls:
                                if temp_rect.colliderect(wall):
                                    wall_collision = True
                                    break

                            # 如果没有墙体碰撞，应用新位置
                            if not wall_collision:
                                enemy.rect.x = new_x
                                enemy.rect.y = new_y
                                print(f"敌人被击退了 {self.knockback_distance} 像素")

class Fireball(pygame.sprite.Sprite):
    def __init__(self, start_pos, direction, damage, game=None):
        super().__init__()
        self.position = pygame.math.Vector2(start_pos)
        self.direction = direction
        self.speed = 8
        self.damage = damage
        self.game = game

        # 加载火球PNG图像（带黑色透明处理）
        try:
            self.image = load_image_with_transparency(IMAGE_PATHS["fire_effect"])
            if self.image:
                # 调整大小为适合的尺寸
                self.image = pygame.transform.scale(self.image, (40, 40))
                # 重新设置透明色（缩放后可能丢失透明设置）
                self.image.set_colorkey((0, 0, 0))
                print(f"成功加载火球图像: {IMAGE_PATHS['fire_effect']}, 黑色已透明")
            else:
                raise Exception("图像加载失败")
        except Exception as e:
            print(f"加载火球图像失败: {e}")
            # 创建默认图像（带透明背景）
            self.image = pygame.Surface((40, 40), pygame.SRCALPHA)
            pygame.draw.circle(self.image, (255, 100, 0), (20, 20), 20)
            # 默认图像也设置黑色透明
            self.image.set_colorkey((0, 0, 0))

        self.rect = self.image.get_rect(center=start_pos)

        # 设置速度
        self.velocity = direction * self.speed

        # 设置生命周期和渐隐效果
        self.creation_time = pygame.time.get_ticks()
        self.lifetime = 3000  # 3秒后消失
        self.fade_start_time = self.creation_time + 2000  # 2秒后开始渐隐
        self.alpha = 255
        self.original_image = self.image.copy()  # 保存原始图像

        print(f"创建火球: 位置={start_pos}, 方向={direction}, 伤害={damage}")

    def update(self, dt=16):
        current_time = pygame.time.get_ticks()

        # 更新位置
        self.position += self.velocity
        self.rect.center = (int(self.position.x), int(self.position.y))

        # 处理渐隐效果
        if current_time > self.fade_start_time:
            # 计算剩余生命周期的百分比
            remaining_life = (self.creation_time + self.lifetime - current_time) / (self.lifetime - (self.fade_start_time - self.creation_time))
            self.alpha = max(0, int(255 * remaining_life))

            # 应用透明度
            self.image = self.original_image.copy()
            self.image.set_alpha(self.alpha)

        # 检查生命周期
        if current_time - self.creation_time > self.lifetime:
            self.kill()
            return

        # 检查墙体碰撞
        if hasattr(self, 'game') and self.game and hasattr(self.game, 'level_system'):
            for wall in self.game.level_system.walls:
                if isinstance(wall, pygame.Rect) and self.rect.colliderect(wall):
                    print(f"火球击中墙体，位置: {self.rect.center}")
                    self.kill()
                    return

            # 检查敌人碰撞
            for enemy in self.game.level_system.enemies:
                if self.rect.colliderect(enemy.rect):
                    # 使用新的增强伤害系统（适中的击退）
                    if hasattr(self.game, 'combat_system'):
                        is_dead = self.game.combat_system.apply_damage_with_effects(
                            enemy, self.damage, self.rect.center,
                            knockback_distance=25,  # 增加击退距离让效果更明显
                            enable_knockback=True
                        )
                        print(f"火球击中敌人! 造成 {self.damage} 点伤害，敌人HP: {enemy.hp}")

                        if is_dead:
                            print(f"敌人被火球击杀！")
                    else:
                        # 备用方案
                        enemy.hp -= self.damage
                        print(f"火球击中敌人! 造成 {self.damage} 点伤害，敌人HP: {enemy.hp}")

                    self.kill()
                    return

class LargeFireball(pygame.sprite.Sprite):
    def __init__(self, start_pos, dx, dy, damage):
        super().__init__()

        # 加载火球图像
        self.image_path = IMAGE_PATHS["fire_effect"]
        print(f"尝试加载火球图像: {self.image_path}")

        try:
            # 加载PNG图像
            self.original_image = pygame.image.load(self.image_path).convert_alpha()
            print(f"加载静态火球图像: {self.image_path}")

            # 调整图像大小为160×128像素
            self.original_image = pygame.transform.scale(self.original_image, (160, 128))
        except Exception as e:
            print(f"加载火球图像失败: {e}")
            # 创建默认图像
            self.original_image = pygame.Surface((160, 128), pygame.SRCALPHA)
            pygame.draw.ellipse(self.original_image, (255, 69, 0, 200), self.original_image.get_rect())

        # 根据移动方向旋转图像
        self.direction = pygame.math.Vector2(dx, dy)
        if self.direction.length() > 0:
            self.direction.normalize_ip()

        # 计算旋转角度
        angle = math.degrees(math.atan2(-self.direction.y, self.direction.x))
        self.image = pygame.transform.rotate(self.original_image, angle)

        # 设置位置和速度
        self.rect = self.image.get_rect(center=start_pos)
        self.position = pygame.math.Vector2(start_pos)
        self.velocity = pygame.math.Vector2(dx, dy)

        # 设置伤害和其他属性
        self.damage = damage
        self.creation_time = pygame.time.get_ticks()
        self.lifetime = 3000  # 火球持续3秒
        self.game = None

        # 渐变消失相关
        self.fade_start_time = self.creation_time + 2000  # 2秒后开始渐变
        self.alpha = 255  # 初始透明度
        self.has_hit = False

        print(f"创建大型火球: 位置={start_pos}, 方向=({dx}, {dy}), 伤害={damage}")

    def update(self):
        current_time = pygame.time.get_ticks()

        # 更新位置
        self.position += self.velocity
        self.rect.center = (int(self.position.x), int(self.position.y))

        # 处理渐变消失效果
        if current_time > self.fade_start_time:
            # 计算剩余生命周期的百分比
            remaining_life = (self.creation_time + self.lifetime - current_time) / (self.lifetime - (self.fade_start_time - self.creation_time))
            self.alpha = max(0, int(255 * remaining_life))

            # 应用透明度
            self.image = self.image.copy()
            self.image.fill((255, 255, 255, self.alpha), None, pygame.BLEND_RGBA_MULT)

        # 检查是否超出屏幕边界
        if (self.rect.right < 0 or self.rect.left > SCREEN_WIDTH or
            self.rect.bottom < 0 or self.rect.top > SCREEN_HEIGHT):
            self.kill()
            return

        # 检查生命周期
        if current_time - self.creation_time > self.lifetime:
            self.kill()
            return

        # 检查墙体碰撞
        if hasattr(self, 'game') and self.game and hasattr(self.game, 'level_system'):
            for wall in self.game.level_system.walls:
                if self.rect.colliderect(wall):
                    self.has_hit = True
                    self.kill()
                    return

        # 检查敌人碰撞 - 已经在上面的代码中处理了，这里不再重复处理
        # 注意: 为了防止重复伤害，这段代码已被移除
        pass

class EarthWall(pygame.sprite.Sprite):
    def __init__(self, position, dir_x, dir_y, duration):
        super().__init__()
        self.position = pygame.math.Vector2(position)
        self.duration = duration
        self.creation_time = pygame.time.get_ticks()
        self.game = None  # 将在使用时设置

        # 根据方向确定墙的尺寸和位置
        if dir_x != 0:  # 水平墙
            self.image = pygame.Surface((100, 40), pygame.SRCALPHA)
            pygame.draw.rect(self.image, (139, 69, 19, 200), (0, 0, 100, 40))
            self.rect = self.image.get_rect(center=position)
        else:  # 垂直墙
            self.image = pygame.Surface((40, 100), pygame.SRCALPHA)
            pygame.draw.rect(self.image, (139, 69, 19, 200), (0, 0, 40, 100))
            self.rect = self.image.get_rect(center=position)

        # 碰撞矩形引用，用于在update中移除
        self.collision_rect = None

        print(f"创建土墙: 位置={position}, 方向=({dir_x}, {dir_y}), 持续时间={duration}ms")

    def update(self, dt=16):
        # 检查生命周期
        current_time = pygame.time.get_ticks()
        if current_time - self.creation_time > self.duration:
            # 移除碰撞矩形
            if self.collision_rect and hasattr(self, 'game') and self.game and hasattr(self.game, 'level_system'):
                if self.collision_rect in self.game.level_system.walls:
                    self.game.level_system.walls.remove(self.collision_rect)
                    print(f"移除墙体碰撞: ID={id(self.collision_rect)}")

            self.kill()
            return

class CombatSystem:
    def __init__(self, player):
        self.player = player
        self.active_effects = []  # 持续效果列表
        self.particle_system = ParticleSystem()
        self.skill_history = []  # 记录最近使用的技能
        self.combo_window = 1000  # 组合技能输入窗口（毫秒）
        self.skill_selection = []  # 关卡结束后的技能选择

        # 添加技能视觉效果列表
        self.visual_effects = []

        # 预加载技能效果图像
        self.skill_images = {}
        self.load_skill_images()

        # 打印加载的技能图像路径，用于调试
        print("已加载技能图像路径:", self.skill_images)

        # 确保player对象有game属性
        if not hasattr(player, 'game'):
            print("警告: player对象没有game属性，技能功能将受限")

        # 移除受伤特效列表
        # self.damage_effects = []  # 删除这一行

    def load_skill_images(self):
        """预加载所有技能效果图像，添加缓存和错误处理"""
        try:
            # 创建图像缓存
            self.image_cache = {}

            # 加载基础技能图像
            for skill_name, skill_data in BASE_SKILLS.items():
                # 加载图标
                icon_key = f"{skill_name}_icon"
                if "icon" in skill_data and skill_data["icon"] in IMAGE_PATHS:
                    self.skill_images[icon_key] = IMAGE_PATHS[skill_data["icon"]]

                # 加载效果图像
                effect_key = f"{skill_name}_effect"
                if "effect" in skill_data and skill_data["effect"] in IMAGE_PATHS:
                    self.skill_images[effect_key] = IMAGE_PATHS[skill_data["effect"]]

            # 加载组合技能图像
            for combo_name, combo_data in COMBO_SKILLS.items():
                # 加载图标
                if "icon" in combo_data:
                    self.skill_images[f"{combo_name}_icon"] = combo_data["icon"]

                # 加载效果图像
                if "effect" in combo_data:
                    self.skill_images[f"{combo_name}_effect"] = combo_data["effect"]

            # 检查图像文件是否存在
            for key, path in list(self.skill_images.items()):
                if not os.path.exists(path):
                    print(f"警告: 图像文件不存在: {path}")
                    # 从字典中移除不存在的图像
                    del self.skill_images[key]

        except Exception as e:
            print(f"加载技能图像失败: {e}")
            import traceback
            traceback.print_exc()

    def update(self, dt=16):
        """更新战斗系统"""
        # 更新持续效果
        self.update_effects()

        # 更新粒子系统
        self.particle_system.update(dt)

        # 更新击退效果
        self.update_knockback_effects()

        # 更新受击效果
        self.update_hit_effects()

        # 移除受伤特效更新
        # self.update_damage_effects()

    def draw_effects(self, surface):
        """绘制所有效果"""
        # 绘制粒子效果
        self.particle_system.draw(surface)

        # 绘制视觉效果
        for effect in self.visual_effects:
            if hasattr(effect, 'draw'):
                effect.draw(surface)

        # 移除受伤特效绘制
        # self.draw_damage_effects(surface)

    def handle_skill_key(self, key):
        """处理技能按键（包括组合技能检测）"""
        current_time = pygame.time.get_ticks()

        # 将pygame键值转换为技能名称
        key_to_skill = {
            pygame.K_1: "metal",
            pygame.K_2: "wood",
            pygame.K_3: "water",
            pygame.K_4: "fire",
            pygame.K_5: "earth"
        }

        if key in key_to_skill:
            skill_name = key_to_skill[key]
            print(f"按下技能键: {skill_name}")

            # 检查组合技能
            combo_result = self.check_combo_input(skill_name, current_time)
            if combo_result:
                return combo_result

            # 检查技能是否解锁
            if skill_name in self.player.unlocked_skills:
                # 检查冷却时间
                if skill_name in self.player.cooldowns:
                    cooldown_end = self.player.cooldowns[skill_name] + BASE_SKILLS[skill_name].get("cooldown", 1000)
                    if current_time < cooldown_end:
                        remaining = (cooldown_end - current_time) / 1000
                        print(f"技能冷却中: {skill_name}, 剩余 {remaining:.1f} 秒")
                        return False

                # 检查MP是否足够
                mp_cost = BASE_SKILLS[skill_name].get("cost", 10)
                if self.player.mp >= mp_cost:
                    # 使用技能
                    self.player.mp -= mp_cost
                    self.player.cooldowns[skill_name] = current_time

                    # 根据技能类型调用相应的方法
                    if skill_name == "metal":
                        self.metal_slash()
                    elif skill_name == "wood":
                        self.healing_tree()
                    elif skill_name == "water":
                        self.water_attack()
                    elif skill_name == "fire":
                        self.fire_attack()
                    elif skill_name == "earth":
                        self.earth_wall()

                    print(f"使用基础技能: {skill_name}, 消耗MP: {mp_cost}, 剩余MP: {self.player.mp}")
                    return True
                else:
                    print(f"MP不足，需要: {mp_cost}, 当前: {self.player.mp}")
            else:
                print(f"技能未解锁: {skill_name}")

        return False

    def check_combo_input(self, skill_name, current_time):
        """检查组合技能输入"""
        # 初始化组合技能相关属性
        if not hasattr(self.player, 'last_skill_key'):
            self.player.last_skill_key = None
        if not hasattr(self.player, 'last_skill_time'):
            self.player.last_skill_time = 0
        if not hasattr(self.player, 'combo_window'):
            self.player.combo_window = 1000  # 1秒组合窗口

        # 检查是否在组合窗口内
        if (self.player.last_skill_key and
            current_time - self.player.last_skill_time <= self.player.combo_window):

            # 检查组合技能
            combo_name = self.check_combo(self.player.last_skill_key, skill_name)
            if combo_name:
                # 检查是否已解锁该组合技能
                if combo_name in self.player.combo_skills:
                    print(f"检测到组合技能: {self.player.last_skill_key} + {skill_name} = {combo_name}")

                    # 重置组合状态
                    self.player.last_skill_key = None
                    self.player.last_skill_time = 0

                    # 使用组合技能
                    return self.use_combo_skill(combo_name)
                else:
                    print(f"组合技能 {combo_name} 未解锁")

        # 记录当前按键作为下一次组合的第一个技能
        self.player.last_skill_key = skill_name
        self.player.last_skill_time = current_time

        return False

    def check_combo(self, skill1, skill2):
        """检查两个技能是否可以组合"""
        # 相同技能组合
        if skill1 == skill2:
            return f"{skill1}_{skill2}"

        # 不同技能组合
        combo1 = f"{skill1}_{skill2}"
        combo2 = f"{skill2}_{skill1}"

        if combo1 in COMBO_SKILLS:
            return combo1
        elif combo2 in COMBO_SKILLS:
            return combo2

        return None

    def use_skill(self, skill_name):
        """使用基础技能"""
        current_time = pygame.time.get_ticks()

        # 确保技能存在于BASE_SKILLS中
        if skill_name not in BASE_SKILLS:
            print(f"错误: 技能 {skill_name} 不存在")
            return False

        skill = BASE_SKILLS[skill_name]

        # 检查冷却和法力值
        if skill_name in self.player.cooldowns:
            return False

        if self.player.mp < skill["cost"]:
            return False

        # 消耗法力值并设置冷却
        self.player.mp -= skill["cost"]
        self.player.cooldowns[skill_name] = current_time

        # 根据技能类型执行不同效果
        try:
            if skill_name == "metal":
                # 金属斩击
                self.metal_slash()
            elif skill_name == "wood":
                # 木系治疗术
                self.healing_tree()
            elif skill_name == "water":
                # 水滴攻击
                self.water_attack()
            elif skill_name == "fire":
                # 火球术
                self.fire_attack()
            elif skill_name == "earth":
                # 土墙术
                self.earth_wall()

            # 显示技能名称
            if hasattr(self, 'show_skill_name'):
                self.show_skill_name(skill["name"], ELEMENT_COLORS[skill_name])

            return True
        except Exception as e:
            print(f"技能 {skill_name} 释放失败: {e}")
            # 技能释放失败时恢复MP和冷却
            self.player.mp += skill["cost"]
            if skill_name in self.player.cooldowns:
                del self.player.cooldowns[skill_name]
            return False

    def use_combo_skill(self, combo_name):
        """使用组合技能"""
        current_time = pygame.time.get_ticks()

        # 确保技能存在于COMBO_SKILLS中
        if combo_name not in COMBO_SKILLS:
            print(f"错误: 组合技能 {combo_name} 不存在")
            return False

        skill = COMBO_SKILLS[combo_name]

        # 检查冷却和法力值
        if combo_name in self.player.cooldowns:
            cooldown_time = skill["cooldown"]
            elapsed = current_time - self.player.cooldowns[combo_name]
            if elapsed < cooldown_time:
                print(f"技能 {skill['name']} 还在冷却中，剩余 {(cooldown_time - elapsed) / 1000:.1f} 秒")
                return False

        if self.player.mp < skill["cost"]:
            print(f"法力值不足，需要 {skill['cost']} 点法力值")
            return False

        # 消耗法力值并设置冷却
        self.player.mp -= skill["cost"]
        self.player.cooldowns[combo_name] = current_time

        # 根据组合技能类型执行不同效果
        try:
            if combo_name == "metal_wood":
                # 疾风剑刃：向前发射一道螺旋状的利刃
                self.wind_blade()
            elif combo_name == "metal_earth":
                # 坚岩护盾：环绕玩家形成护盾
                self.rock_shield()
            elif combo_name == "wood_water":
                # 生命缠绕：在屏幕中间生成一朵花，持续回血
                self.life_embrace()
            elif combo_name == "wood_fire":
                # 焚天烈焰：身前生成火焰龙卷
                self.burning_flame()
            elif combo_name == "water_fire":
                # 蒸汽爆炸：范围性爆炸伤害
                self.steam_explosion()
            elif combo_name == "water_earth":
                # 沼泽陷阱：身前形成减速泥潭
                self.swamp_trap()
            elif combo_name == "metal_metal":
                # 天降神兵：从天降下金色大剑
                self.heaven_sword()
            elif combo_name == "wood_wood":
                # 森罗万象：召唤树人吸引仇恨
                self.tree_guardian()
            elif combo_name == "water_water":
                # 绝对零度：冰封全场敌人
                self.absolute_zero()
            elif combo_name == "fire_fire":
                # 火龙术：生成火龙秒杀敌人
                self.fire_dragon()
            elif combo_name == "earth_earth":
                # 地震术：全屏地面震动攻击
                self.earthquake()

            # 显示技能名称
            self.show_skill_name(skill["name"], ELEMENT_COLORS.get(combo_name.split("_")[0], (255, 255, 255)))

            return True
        except Exception as e:
            print(f"组合技能 {combo_name} 释放失败: {e}")
            import traceback
            traceback.print_exc()

            # 技能释放失败时恢复MP和冷却
            self.player.mp += skill["cost"]
            if combo_name in self.player.cooldowns:
                del self.player.cooldowns[combo_name]

            return False

    def offer_new_skills(self):
        """关卡结束后提供技能选择"""
        # 获取所有可用的组合技能
        available_combos = []
        for combo_name in COMBO_SKILLS:
            # 检查是否已经解锁
            if combo_name not in self.player.combo_skills:
                available_combos.append(combo_name)

        # 如果没有可用的组合技能，返回
        if not available_combos:
            print("没有可用的新组合技能")
            self.skill_selection = []  # 确保设置为空列表
            return

        # 随机选择三个组合技能（或少于三个，如果可用技能不足）
        import random
        num_choices = min(3, len(available_combos))
        self.skill_selection = random.sample(available_combos, num_choices)

        # 设置游戏状态为技能选择
        if hasattr(self.player, 'game'):
            self.player.game.game_state = "SKILL_SELECT"
            print(f"提供技能选择: {self.skill_selection}")
            print(f"游戏状态已切换为: {self.player.game.game_state}")

    def select_skill(self, index):
        """玩家选择了一个技能"""
        if 0 <= index < len(self.skill_selection):
            selected_skill = self.skill_selection[index]
            # 将技能添加到玩家的组合技能列表
            self.player.combo_skills.append(selected_skill)
            print(f"玩家选择了技能: {selected_skill}")

            # 重置技能选择列表
            self.skill_selection = []

            # 恢复游戏状态并进入下一关
            if hasattr(self.player, 'game'):
                self.player.game.proceed_to_next_level()  # 使用新方法进入下一关
        else:
            print(f"无效的技能索引: {index}")

    def show_skill_name(self, skill_name, color):
        """显示技能名称"""
        # 这个函数将在视图系统中实现
        pass

    # 基础技能效果实现
    def metal_slash(self):
        """金属斩击效果"""
        try:
            # 获取玩家位置和方向
            player_pos = self.player.rect.center
            direction = pygame.math.Vector2(0, 0)

            # 根据玩家当前方向设置斩击方向
            if self.player.current_direction == "up":
                direction.y = -1
            elif self.player.current_direction == "down":
                direction.y = 1
            elif self.player.current_direction == "left":
                direction.x = -1
            elif self.player.current_direction == "right":
                direction.x = 1

            # 设置斩击范围和宽度
            slash_range = 100  # 斩击范围
            slash_width = 60   # 斩击宽度

            # 计算斩击中心点
            slash_center = (
                player_pos[0] + direction.x * slash_range / 2,
                player_pos[1] + direction.y * slash_range / 2
            )

            # 创建斩击矩形
            if direction.x != 0:  # 水平斩击
                slash_rect = pygame.Rect(0, 0, slash_range, slash_width)
                slash_rect.centery = slash_center[1]
                if direction.x > 0:  # 向右
                    slash_rect.left = player_pos[0]
                else:  # 向左
                    slash_rect.right = player_pos[0]
            else:  # 垂直斩击
                slash_rect = pygame.Rect(0, 0, slash_width, slash_range)
                slash_rect.centerx = slash_center[0]
                if direction.y > 0:  # 向下
                    slash_rect.top = player_pos[1]
                else:  # 向上
                    slash_rect.bottom = player_pos[1]

            # 先创建视觉效果（确保总是显示）
            metal_slash_effect = MetalSlashEffect(
                slash_center,
                IMAGE_PATHS["metal_effect"],
                500,  # 持续500毫秒
                self.player.current_direction
            )

            # 添加到游戏中
            if hasattr(self.player, 'game') and hasattr(self.player.game, 'level_system'):
                self.player.game.level_system.add_effect(metal_slash_effect)
                print(f"金属斩击视觉效果已添加")
            else:
                print("警告: 无法添加视觉效果")

            # 创建粒子效果
            self.particle_system.create_metal_particles(slash_center, 30)

            # 然后检测敌人碰撞并造成伤害
            damage = BASE_SKILLS["metal"]["damage"]

            # 安全地访问敌人列表
            if hasattr(self.player, 'game') and hasattr(self.player.game, 'level_system'):
                enemies_hit = False
                for enemy in self.player.game.level_system.enemies:
                    if slash_rect.colliderect(enemy.rect):
                        # 使用新的增强伤害系统（明显击退）
                        is_dead = self.apply_damage_with_effects(
                            enemy, damage, self.player.rect.center,
                            knockback_distance=20,  # 增加击退距离让效果更明显
                            enable_knockback=True
                        )
                        enemies_hit = True
                        print(f"金属斩击击中敌人! 造成 {damage} 点伤害，敌人剩余HP: {enemy.hp}")

                        if is_dead:
                            print(f"敌人被金属斩击击杀！")

                if enemies_hit:
                    print(f"金属斩击成功命中敌人，造成 {damage} 点伤害")
                else:
                    print(f"金属斩击未命中敌人，斩击区域: {slash_rect}")

            print(f"金属斩击技能释放成功，位置: {slash_center}, 方向: {self.player.current_direction}")
        except Exception as e:
            print(f"金属斩击技能释放失败: {e}")
            import traceback
            traceback.print_exc()

    def healing_tree(self):
        """木系治疗术效果"""
        try:
            # 在玩家位置创建治疗树
            tree_pos = self.player.rect.center

            # 添加持续效果
            current_time = pygame.time.get_ticks()
            healing_effect = {
                "type": "healing_tree",
                "position": tree_pos,
                "heal_rate": BASE_SKILLS["wood"]["heal_rate"],
                "end_time": current_time + 5000,  # 持续5秒
                "last_tick": current_time
            }
            self.active_effects.append(healing_effect)

            # 创建治疗树视觉效果
            healing_tree_effect = HealingTreeEffect(
                tree_pos,
                IMAGE_PATHS["wood_effect"],
                5000  # 持续5秒
            )

            # 添加到游戏中
            if hasattr(self.player, 'game') and hasattr(self.player.game, 'level_system'):
                self.player.game.level_system.add_effect(healing_tree_effect)
                print(f"治疗树视觉效果已添加")
            else:
                print("警告: 无法添加治疗树视觉效果")

            # 创建粒子效果
            self.particle_system.create_wood_particles(tree_pos, 25)

            print(f"木系治疗术释放，每秒回复 {BASE_SKILLS['wood']['heal_rate']} 点生命值，持续5秒")
        except Exception as e:
            print(f"木系治疗术技能释放失败: {e}")

    def water_attack(self):
        """水波攻击效果"""
        try:
            # 获取玩家位置
            player_pos = self.player.rect.center

            # 创建水波效果
            water_wave = WaterWave(
                player_pos,
                BASE_SKILLS["water"]["damage"],
                self.player.game if hasattr(self.player, 'game') else None
            )

            # 设置游戏引用
            if hasattr(self.player, 'game'):
                water_wave.game = self.player.game

                # 添加到游戏中
                if hasattr(self.player.game, 'level_system'):
                    self.player.game.level_system.add_projectile(water_wave)
                    print(f"水波攻击已添加到投射物列表")
                else:
                    print("警告: 无法访问level_system")
            else:
                print("警告: 无法访问game对象")
        except Exception as e:
            print(f"水波攻击失败: {e}")
            import traceback
            traceback.print_exc()

    def fire_attack(self):
        """火球攻击效果"""
        try:
            # 获取玩家位置和朝向
            player_pos = self.player.rect.center
            direction = pygame.math.Vector2(0, 0)

            # 根据玩家朝向确定方向
            if self.player.current_direction == "up":
                direction.y = -1
            elif self.player.current_direction == "down":
                direction.y = 1
            elif self.player.current_direction == "left":
                direction.x = -1
            elif self.player.current_direction == "right":
                direction.x = 1

            # 如果没有方向，默认向右
            if direction.length() == 0:
                direction.x = 1

            # 创建火球
            fireball = Fireball(
                player_pos,
                direction,
                BASE_SKILLS["fire"]["damage"],
                self.player.game if hasattr(self.player, 'game') else None
            )

            # 设置游戏引用
            if hasattr(self.player, 'game'):
                fireball.game = self.player.game

                # 添加到游戏中
                if hasattr(self.player.game, 'level_system'):
                    self.player.game.level_system.add_projectile(fireball)
                    print(f"火球已添加到投射物列表")
                else:
                    print("警告: 无法访问level_system")
            else:
                print("警告: 无法访问game对象")
        except Exception as e:
            print(f"火球攻击失败: {e}")
            import traceback
            traceback.print_exc()

    def earth_wall(self):
        """土墙术效果"""
        try:
            # 获取玩家前方位置
            player_pos = self.player.rect.center
            direction = pygame.math.Vector2(0, 0)

            # 根据玩家朝向确定墙的方向
            if self.player.current_direction == "up":
                direction.y = -1
            elif self.player.current_direction == "down":
                direction.y = 1
            elif self.player.current_direction == "left":
                direction.x = -1
            elif self.player.current_direction == "right":
                direction.x = 1

            # 计算墙的位置
            wall_distance = 60  # 墙与玩家的距离
            wall_pos = (player_pos[0] + direction.x * wall_distance,
                       player_pos[1] + direction.y * wall_distance)

            # 获取技能持续时间
            wall_duration = BASE_SKILLS["earth"]["duration"]
            print(f"土墙术持续时间: {wall_duration}ms, 位置: {wall_pos}")

            # 创建土墙视觉效果
            earth_wall_effect = EarthWallEffect(
                wall_pos,
                IMAGE_PATHS["earth_effect"],
                wall_duration,
                direction
            )

            # 创建粒子效果
            self.particle_system.create_earth_particles(wall_pos, 20)

            # 安全地访问level_system
            if hasattr(self.player, 'game') and hasattr(self.player.game, 'level_system'):
                # 创建土墙碰撞体
                wall = EarthWall(wall_pos, direction.x, direction.y, wall_duration)
                wall.game = self.player.game

                # 添加视觉效果
                self.player.game.level_system.add_effect(earth_wall_effect)

                # 添加碰撞体
                self.player.game.level_system.add_effect(wall)

                # 将墙体添加到临时墙体列表中，使其具有碰撞属性
                if not hasattr(self.player.game.level_system, 'temporary_walls'):
                    self.player.game.level_system.temporary_walls = []

                # 添加到临时墙体列表
                temp_wall_data = {
                    'rect': wall.rect,
                    'wall_object': wall,
                    'creation_time': pygame.time.get_ticks(),
                    'duration': wall_duration
                }
                self.player.game.level_system.temporary_walls.append(temp_wall_data)

                print(f"土墙术成功创建: 位置={wall_pos}, 持续时间={wall_duration}ms")
                print(f"临时墙体数量: {len(self.player.game.level_system.temporary_walls)}")
            else:
                print("警告: 无法访问level_system，土墙术失败")
        except Exception as e:
            print(f"土墙术技能释放失败: {e}")
            import traceback
            traceback.print_exc()

    # 组合技能效果实现
    def wind_blade(self):
        """疾风剑刃效果 (金+木)"""
        # 获取玩家位置和方向
        player_pos = self.player.rect.center
        direction = pygame.math.Vector2(0, 0)

        if self.player.current_direction == "up":
            direction.y = -1
        elif self.player.current_direction == "down":
            direction.y = 1
        elif self.player.current_direction == "left":
            direction.x = -1
        elif self.player.current_direction == "right":
            direction.x = 1

        # 创建螺旋利刃投射物
        if hasattr(self.player, 'game') and hasattr(self.player.game, 'level_system'):
            # 加载效果图像
            try:
                effect_path = COMBO_SKILLS["metal_wood"]["effect"]
                blade = SpiralBlade(
                    player_pos,
                    direction.x * 8,  # 速度
                    direction.y * 8,
                    COMBO_SKILLS["metal_wood"]["damage"],
                    effect_path
                )

                # 设置游戏引用
                blade.game = self.player.game

                # 添加到游戏中
                self.player.game.level_system.add_projectile(blade)
            except Exception as e:
                print(f"创建疾风剑刃失败: {e}")

    def rock_shield(self):
        """坚岩护盾效果 (金+土)"""
        # 创建护盾效果
        current_time = pygame.time.get_ticks()
        shield_effect = {
            "type": "shield",
            "shield_value": COMBO_SKILLS["metal_earth"]["shield"],
            "end_time": current_time + COMBO_SKILLS["metal_earth"]["duration"],
            "last_tick": current_time
        }
        self.active_effects.append(shield_effect)

        # 添加视觉效果
        try:
            effect_path = COMBO_SKILLS["metal_earth"]["effect"]
            shield = ShieldEffect(
                self.player,
                effect_path,
                COMBO_SKILLS["metal_earth"]["duration"]
            )

            # 添加到游戏中
            if hasattr(self.player, 'game') and hasattr(self.player.game, 'level_system'):
                self.player.game.level_system.add_effect(shield)
        except Exception as e:
            print(f"创建坚岩护盾失败: {e}")

    def life_embrace(self):
        """生命缠绕效果 (木+水)"""
        # 在屏幕中间创建治疗花
        screen_center = (SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)

        # 创建持续治疗效果
        current_time = pygame.time.get_ticks()
        healing_effect = {
            "type": "healing_flower",
            "position": screen_center,
            "heal_rate": 8,  # 每秒回血量
            "total_healing": COMBO_SKILLS["wood_water"]["healing"],
            "end_time": current_time + 5000,  # 持续5秒
            "last_tick": current_time
        }
        self.active_effects.append(healing_effect)

        # 添加视觉效果
        try:
            effect_path = COMBO_SKILLS["wood_water"]["effect"]
            flower = HealingFlower(
                screen_center,
                effect_path,
                5000  # 持续5秒
            )

            # 添加到游戏中
            if hasattr(self.player, 'game') and hasattr(self.player.game, 'level_system'):
                self.player.game.level_system.add_effect(flower)
        except Exception as e:
            print(f"创建生命缠绕失败: {e}")

    def burning_flame(self):
        """焚天烈焰效果 (木+火) - 在玩家前方进行范围攻击"""
        print("\n🔥 焚天烈焰开始执行...")

        try:
            # 检查游戏引用
            if not hasattr(self.player, 'game') or not self.player.game:
                print("❌ 玩家没有游戏引用")
                return

            if not hasattr(self.player.game, 'level_system'):
                print("❌ 没有关卡系统")
                return

            # 获取玩家位置和方向
            player_pos = self.player.rect.center
            direction = pygame.math.Vector2(0, 0)

            if self.player.current_direction == "up":
                direction.y = -1
            elif self.player.current_direction == "down":
                direction.y = 1
            elif self.player.current_direction == "left":
                direction.x = -1
            elif self.player.current_direction == "right":
                direction.x = 1
            else:
                # 默认向下
                direction.y = 1

            # 计算火焰攻击的位置（玩家前方120像素）
            attack_distance = 120
            flame_center = (
                player_pos[0] + direction.x * attack_distance,
                player_pos[1] + direction.y * attack_distance
            )

            print(f"🎯 玩家位置: {player_pos}, 方向: {self.player.current_direction}")
            print(f"🔥 火焰攻击位置: {flame_center}")

            # 计算攻击范围（圆形区域）
            attack_radius = 80  # 攻击半径

            # 创建火焰范围攻击特效
            try:
                effect_path = COMBO_SKILLS["wood_fire"]["effect"]
                print(f"🎨 加载特效: {effect_path}")

                # 创建火焰攻击特效
                flame_effect = BurningFlameEffect(
                    flame_center,
                    effect_path,
                    COMBO_SKILLS["wood_fire"]["damage"],
                    attack_radius,
                    3000  # 持续3秒
                )

                # 设置游戏引用
                flame_effect.game = self.player.game

                # 添加到游戏中
                self.player.game.level_system.add_effect(flame_effect)
                print(f"✨ 焚天烈焰特效创建成功")

            except Exception as e:
                print(f"❌ 创建特效失败: {e}")

            # 立即对范围内敌人造成伤害
            print(f"💥 开始范围伤害检测...")
            enemies = self.player.game.level_system.enemies
            print(f"📊 当前敌人数量: {len(enemies)}")

            enemies_hit = 0
            damage_value = COMBO_SKILLS['wood_fire']['damage']

            for i, enemy in enumerate(list(enemies)):
                if enemy.hp <= 0:
                    continue

                # 计算与敌人的距离
                enemy_pos = pygame.math.Vector2(enemy.rect.center)
                flame_pos = pygame.math.Vector2(flame_center)
                distance = flame_pos.distance_to(enemy_pos)

                print(f"  📍 敌人{i+1}位置: {enemy_pos}, 距离: {distance:.1f}")

                # 如果在攻击范围内，造成伤害
                if distance <= attack_radius:
                    # 使用新的增强伤害系统（火焰的热浪击退）
                    is_dead = self.apply_damage_with_effects(
                        enemy, damage_value, flame_center,
                        knockback_distance=18,  # 增加击退距离让效果更明显
                        enable_knockback=True
                    )
                    enemies_hit += 1

                    print(f"  ⚡ 敌人{i+1}被火焰击中! 受到 {damage_value} 点伤害，剩余HP: {enemy.hp}")

                    if is_dead:
                        print(f"    💀 敌人{i+1}被火焰烧死！")
                else:
                    print(f"  ❌ 敌人{i+1}不在范围内 (距离: {distance:.1f} > {attack_radius})")

            print(f"🎯 焚天烈焰完成，击中 {enemies_hit} 个敌人，伤害: {damage_value}")

            # 再次检查敌人状态
            print(f"🔍 伤害后敌人状态:")
            for i, enemy in enumerate(current_enemies):
                print(f"  敌人{i+1}: HP={enemy.hp}")

        except Exception as e:
            print(f"❌ 焚天烈焰执行失败: {e}")
            import traceback
            traceback.print_exc()

    def steam_explosion(self):
        """蒸汽爆炸效果 (水+火)"""
        # 获取玩家位置
        player_pos = self.player.rect.center

        # 创建爆炸效果
        try:
            effect_path = COMBO_SKILLS["water_fire"]["effect"]
            explosion = SteamExplosion(
                player_pos,
                effect_path,
                COMBO_SKILLS["water_fire"]["damage"],
                200  # 爆炸范围
            )

            # 添加到游戏中
            if hasattr(self.player, 'game') and hasattr(self.player.game, 'level_system'):
                explosion.game = self.player.game
                self.player.game.level_system.add_effect(explosion)

                # 立即对范围内敌人造成伤害
                explosion.apply_damage()
        except Exception as e:
            print(f"创建蒸汽爆炸失败: {e}")

    def swamp_trap(self):
        """沼泽陷阱效果 (水+土)"""
        # 获取玩家位置和方向
        player_pos = self.player.rect.center
        direction = pygame.math.Vector2(0, 0)

        if self.player.current_direction == "up":
            direction.y = -1
        elif self.player.current_direction == "down":
            direction.y = 1
        elif self.player.current_direction == "left":
            direction.x = -1
        elif self.player.current_direction == "right":
            direction.x = 1

        # 计算沼泽陷阱的位置（玩家前方）
        swamp_pos = (
            player_pos[0] + direction.x * 100,
            player_pos[1] + direction.y * 100
        )

        # 创建沼泽陷阱效果
        try:
            effect_path = COMBO_SKILLS["water_earth"]["effect"]
            swamp = SwampTrap(
                swamp_pos,
                effect_path,
                8000  # 持续8秒
            )

            # 添加到游戏中
            if hasattr(self.player, 'game') and hasattr(self.player.game, 'level_system'):
                swamp.game = self.player.game
                self.player.game.level_system.add_effect(swamp)
        except Exception as e:
            print(f"创建沼泽陷阱失败: {e}")

    def heaven_sword(self):
        """天降神兵效果 (金+金) - 全屏攻击"""
        # 在屏幕中央创建天降神剑特效
        screen_center = (SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)

        # 创建天降神剑效果
        try:
            effect_path = COMBO_SKILLS["metal_metal"]["effect"]
            sword = HeavenlySword(
                screen_center,
                effect_path,
                COMBO_SKILLS["metal_metal"]["damage"],
                9999  # 全屏范围
            )

            # 添加到游戏中
            if hasattr(self.player, 'game') and hasattr(self.player.game, 'level_system'):
                sword.game = self.player.game
                self.player.game.level_system.add_effect(sword)
                print(f"天降神兵释放成功，全屏攻击，伤害: {COMBO_SKILLS['metal_metal']['damage']}")

                # 天降神兵将在1.4秒后在HeavenlySword类中造成伤害
                print(f"天降神兵特效已创建，将在1.4秒后对敌人造成伤害")
            else:
                print("警告: 无法访问游戏系统，天降神兵失败")
        except Exception as e:
            print(f"创建天降神兵失败: {e}")

    def tree_guardian(self):
        """森罗万象效果 (木+木)"""
        # 获取玩家位置
        player_pos = self.player.rect.center

        # 创建树人效果
        try:
            effect_path = COMBO_SKILLS["wood_wood"]["effect"]
            guardian = TreeGuardian(
                player_pos,
                effect_path,
                10000  # 持续10秒
            )

            # 添加到游戏中
            if hasattr(self.player, 'game') and hasattr(self.player.game, 'level_system'):
                guardian.game = self.player.game
                self.player.game.level_system.add_effect(guardian)
        except Exception as e:
            print(f"创建森罗万象失败: {e}")

    def absolute_zero(self):
        """绝对零度效果 (水+水)"""
        # 冻结所有敌人
        if hasattr(self.player, 'game') and hasattr(self.player.game, 'level_system'):
            for enemy in self.player.game.level_system.enemies:
                enemy.frozen = True
                enemy.frozen_until = pygame.time.get_ticks() + 5000  # 冻结5秒
                # 对敌人造成伤害
                enemy.hp -= COMBO_SKILLS["water_water"]["damage"]
                print(f"绝对零度击中敌人! 造成 {COMBO_SKILLS['water_water']['damage']} 点伤害，敌人剩余HP: {enemy.hp}")

            # 创建冰冻效果
            try:
                effect_path = COMBO_SKILLS["water_water"]["effect"]
                freeze = FreezeEffect(
                    (SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2),
                    effect_path,
                    5000  # 持续5秒
                )

                # 添加到游戏中
                freeze.game = self.player.game
                self.player.game.level_system.add_effect(freeze)
            except Exception as e:
                print(f"创建绝对零度失败: {e}")
        else:
            print("警告: 无法访问游戏系统，绝对零度失败")


    def fire_dragon(self):
        """火龙术效果 - 按照绝对零度模式实现（立即造成伤害）"""
        print("\n🐉 火龙术开始执行...")

        # 立即对所有敌人造成伤害（模仿绝对零度）
        if hasattr(self.player, 'game') and hasattr(self.player.game, 'level_system'):
            damage_value = COMBO_SKILLS['fire_fire']['damage']
            enemies_hit = 0

            for enemy in self.player.game.level_system.enemies:
                # 直接修改敌人HP（模仿绝对零度的方式）
                enemy.hp -= damage_value
                enemies_hit += 1
                print(f"火龙术击中敌人! 造成 {damage_value} 点伤害，敌人剩余HP: {enemy.hp}")

                # 添加红色粒子效果
                self.add_damage_effect(enemy.rect.center, damage_value)

            print(f"火龙术共击中 {enemies_hit} 个敌人")

            # 创建火龙视觉特效（模仿绝对零度）
            try:
                effect_path = COMBO_SKILLS["fire_fire"]["effect"]
                print(f"尝试创建火龙特效，路径: {effect_path}")

                fire_dragon_effect = FireDragon(
                    (SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2),  # start_pos
                    effect_path,  # image_path
                    5000  # duration - 持续5秒
                )

                # 设置游戏引用
                fire_dragon_effect.game = self.player.game

                # 添加到游戏中
                if hasattr(self.player.game, 'level_system'):
                    self.player.game.level_system.add_effect(fire_dragon_effect)
                    print(f"火龙特效成功添加到游戏中，持续5秒")
                else:
                    print(f"无法添加火龙特效：没有level_system")

            except Exception as e:
                print(f"创建火龙特效失败: {e}")
                import traceback
                traceback.print_exc()
        else:
            print("警告: 无法访问游戏系统，火龙术失败")

    def earthquake(self):
        """地震术效果 (土+土) - 先显示动画，0.9秒后造成伤害"""
        print("\n🌍 地震术开始执行...")

        # 先创建地震视觉特效，0.9秒后造成伤害
        if hasattr(self.player, 'game') and hasattr(self.player.game, 'level_system'):
            try:
                effect_path = COMBO_SKILLS["earth_earth"]["effect"]
                earthquake_effect = EarthquakeEffect(
                    (SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2),
                    effect_path,
                    COMBO_SKILLS['earth_earth']['damage'],
                    900  # 0.9秒后造成伤害
                )

                # 设置游戏引用
                earthquake_effect.game = self.player.game
                self.player.game.level_system.add_effect(earthquake_effect)
                print(f"地震特效创建成功，将0.9秒后对敌人造成伤害")
            except Exception as e:
                print(f"创建地震特效失败: {e}")
        else:
            print("警告: 无法访问游戏系统，地震术失败")

    def add_damage_effect(self, position, damage, attacker_pos=None):
        """添加增强的受伤特效"""
        # 创建红色散射粒子效果（表示扣血）
        particle_count = min(10, max(5, damage // 10))  # 根据伤害调整粒子数量

        self.particle_system.create_particles(
            position[0], position[1],
            (255, 50, 50),  # 明亮红色
            count=particle_count,
            size_range=(3, 6),
            speed_range=(3, 8),
            lifetime_range=(500, 1000),
            gravity=0.15,
            spread_angle=360  # 全方向散射
        )

        # 添加额外的血滴效果
        self.particle_system.create_particles(
            position[0], position[1],
            (200, 0, 0),  # 深红色血滴
            count=3,
            size_range=(2, 4),
            speed_range=(1, 3),
            lifetime_range=(800, 1500),
            gravity=0.2
        )

        print(f"添加增强伤害特效: 位置={position}, 伤害={damage}, 粒子数={particle_count}")

    def apply_knockback(self, target, attacker_pos, knockback_distance=25):
        """应用击退效果"""
        if not hasattr(target, 'rect') or not hasattr(target, 'position'):
            return

        # 计算击退方向
        target_pos = pygame.math.Vector2(target.rect.center)
        attacker_vec = pygame.math.Vector2(attacker_pos)

        # 计算从攻击者指向被攻击者的方向
        direction = target_pos - attacker_vec
        if direction.length() > 0:
            direction = direction.normalize()
        else:
            # 如果位置重合，随机选择一个方向
            direction = pygame.math.Vector2(random.uniform(-1, 1), random.uniform(-1, 1)).normalize()

        # 计算目标位置
        knockback_target = target_pos + direction * knockback_distance

        # 边界检查，确保不超出游戏区域
        knockback_target.x = max(target.rect.width // 2,
                               min(SCREEN_WIDTH - target.rect.width // 2, knockback_target.x))
        knockback_target.y = max(target.rect.height // 2,
                               min(SCREEN_HEIGHT - target.rect.height // 2, knockback_target.y))

        # 检查是否会穿墙（如果有墙体系统）
        if hasattr(self.player, 'game') and hasattr(self.player.game, 'level_system'):
            walls = getattr(self.player.game.level_system, 'walls', [])

            # 创建临时rect来检查碰撞
            temp_rect = target.rect.copy()
            temp_rect.center = knockback_target

            # 检查是否与墙体碰撞
            wall_collision = False
            for wall in walls:
                if temp_rect.colliderect(wall):
                    wall_collision = True
                    break

            if wall_collision:
                # 如果会碰撞，减少击退距离
                knockback_target = target_pos + direction * (knockback_distance * 0.5)
                temp_rect.center = knockback_target

                # 再次检查，如果仍然碰撞则取消击退
                for wall in walls:
                    if temp_rect.colliderect(wall):
                        return  # 取消击退

        # 应用击退效果（使用缓动）
        # 总是重新设置击退数据，允许新的击退覆盖旧的
        target.knockback_data = {
            'start_pos': target_pos.copy(),
            'target_pos': knockback_target.copy(),
            'start_time': pygame.time.get_ticks(),
            'duration': 300,  # 增加到0.3秒，让击退更明显
            'active': True
        }

        print(f"应用击退: 从 {target_pos} 到 {knockback_target}, 距离={knockback_distance}, 方向={direction}")
        print(f"击退数据: 持续时间={target.knockback_data['duration']}ms")

    def update_knockback_effects(self):
        """更新击退效果"""
        if not hasattr(self.player, 'game') or not hasattr(self.player.game, 'level_system'):
            return

        current_time = pygame.time.get_ticks()
        enemies = getattr(self.player.game.level_system, 'enemies', [])

        for enemy in enemies:
            if hasattr(enemy, 'knockback_data') and enemy.knockback_data['active']:
                kb_data = enemy.knockback_data
                elapsed = current_time - kb_data['start_time']

                if elapsed >= kb_data['duration']:
                    # 击退完成
                    enemy.rect.center = kb_data['target_pos']
                    if hasattr(enemy, 'position'):
                        enemy.position = pygame.math.Vector2(kb_data['target_pos'])
                    kb_data['active'] = False
                    # 清除击退标记，允许AI恢复控制
                    enemy.being_knocked_back = False
                else:
                    # 计算当前位置（使用缓动函数）
                    progress = elapsed / kb_data['duration']
                    # 使用ease-out缓动
                    eased_progress = 1 - (1 - progress) ** 3

                    current_pos = kb_data['start_pos'].lerp(kb_data['target_pos'], eased_progress)
                    enemy.rect.center = current_pos
                    if hasattr(enemy, 'position'):
                        enemy.position = pygame.math.Vector2(current_pos)

                    # 标记敌人正在被击退，防止AI覆盖位置
                    enemy.being_knocked_back = True

    def apply_damage_with_effects(self, target, damage, attacker_pos=None, knockback_distance=0, enable_knockback=True):
        """应用伤害并触发所有打击效果"""
        if not hasattr(target, 'hp'):
            return False

        # 应用伤害
        old_hp = target.hp
        target.hp -= damage

        # 添加受击效果（随机抖动）
        self.apply_hit_effect(target)

        # 添加视觉效果（红色粒子）
        self.add_damage_effect(target.rect.center, damage, attacker_pos)

        # 有选择地应用击退效果
        if enable_knockback and knockback_distance > 0 and attacker_pos:
            self.apply_knockback(target, attacker_pos, knockback_distance)
            print(f"应用伤害: {old_hp} -> {target.hp} (伤害: {damage}), 击退: {knockback_distance}像素")
        else:
            print(f"应用伤害: {old_hp} -> {target.hp} (伤害: {damage}), 无击退")

        return target.hp <= 0  # 返回是否死亡

    def apply_hit_effect(self, target):
        """为敌人添加受击效果（随机抖动）"""
        if not hasattr(target, 'rect'):
            return

        # 设置受击效果数据
        target.hit_effect_timer = pygame.time.get_ticks() + 200  # 0.2秒的受击效果
        target.hit_effect_intensity = 3  # 抖动强度
        target.original_pos = target.rect.center  # 保存原始位置

        print(f"应用受击效果: 抖动强度={target.hit_effect_intensity}, 持续200ms")

    def update_hit_effects(self):
        """更新所有敌人的受击效果"""
        if not hasattr(self.player, 'game') or not hasattr(self.player.game, 'level_system'):
            return

        current_time = pygame.time.get_ticks()
        enemies = getattr(self.player.game.level_system, 'enemies', [])

        for enemy in enemies:
            if hasattr(enemy, 'hit_effect_timer') and current_time < enemy.hit_effect_timer:
                # 应用随机抖动效果
                if hasattr(enemy, 'hit_effect_intensity') and hasattr(enemy, 'original_pos'):
                    offset_x = random.randint(-enemy.hit_effect_intensity, enemy.hit_effect_intensity)
                    offset_y = random.randint(-enemy.hit_effect_intensity, enemy.hit_effect_intensity)

                    # 保持在原始位置附近抖动
                    new_x = enemy.original_pos[0] + offset_x
                    new_y = enemy.original_pos[1] + offset_y
                    enemy.rect.center = (new_x, new_y)

                    # 更新position属性（如果存在）
                    if hasattr(enemy, 'position'):
                        enemy.position = pygame.math.Vector2(new_x, new_y)
            elif hasattr(enemy, 'hit_effect_timer'):
                # 受击效果结束，恢复原始位置
                if hasattr(enemy, 'original_pos'):
                    enemy.rect.center = enemy.original_pos
                    if hasattr(enemy, 'position'):
                        enemy.position = pygame.math.Vector2(enemy.original_pos)

                # 清理受击效果数据
                delattr(enemy, 'hit_effect_timer')
                if hasattr(enemy, 'hit_effect_intensity'):
                    delattr(enemy, 'hit_effect_intensity')
                if hasattr(enemy, 'original_pos'):
                    delattr(enemy, 'original_pos')

    def apply_simple_damage(self, target, damage):
        """简单的伤害应用，只有粒子效果和受击效果，无击退"""
        if not hasattr(target, 'hp'):
            return False

        old_hp = target.hp
        target.hp -= damage

        # 添加受击效果（随机抖动）
        self.apply_hit_effect(target)

        # 添加红色粒子效果
        self.add_damage_effect(target.rect.center, damage)

        print(f"简单伤害: {old_hp} -> {target.hp} (伤害: {damage})")

        return target.hp <= 0

    def apply_pure_damage(self, target, damage):
        """纯伤害应用，只有粒子效果，无受击效果和击退（用于全屏技能）"""
        if not hasattr(target, 'hp'):
            return False

        old_hp = target.hp
        target.hp -= damage

        # 只添加红色粒子效果，无受击抖动
        self.add_damage_effect(target.rect.center, damage)

        print(f"纯伤害: {old_hp} -> {target.hp} (伤害: {damage})")

        return target.hp <= 0

    def update_effects(self):
        """更新持续效果"""
        current_time = pygame.time.get_ticks()

        # 使用列表推导式过滤掉已过期的效果
        active_effects = []
        for effect in self.active_effects:
            # 检查效果是否已过期
            if effect["end_time"] > current_time:
                # 根据效果类型执行不同的更新逻辑
                if effect["type"] == "healing_tree":
                    # 治疗树效果 - 每秒恢复生命值
                    if current_time - effect["last_tick"] > 1000:  # 每秒检查一次
                        effect["last_tick"] = current_time
                        # 恢复生命值
                        if self.player.hp < 100:  # 假设最大生命值为100
                            self.player.hp = min(100, self.player.hp + effect["heal_rate"])
                            print(f"治疗树恢复 {effect['heal_rate']} 点生命值，当前HP: {self.player.hp}")

                elif effect["type"] == "shield":
                    # 护盾效果 - 减少受到的伤害
                    pass  # 护盾效果在玩家受伤时处理

                elif effect["type"] == "healing_flower":
                    # 治疗花效果 - 每秒恢复生命值，有总恢复量限制
                    if current_time - effect["last_tick"] > 1000:  # 每秒检查一次
                        effect["last_tick"] = current_time
                        # 计算可恢复的生命值
                        heal_amount = min(effect["heal_rate"], effect["total_healing"])
                        if heal_amount > 0 and self.player.hp < 100:
                            actual_heal = min(heal_amount, 100 - self.player.hp)
                            self.player.hp += actual_heal
                            effect["total_healing"] -= actual_heal
                            print(f"治疗花恢复 {actual_heal} 点生命值，当前HP: {self.player.hp}，剩余治疗量: {effect['total_healing']}")

                # 将未过期的效果添加到新列表
                active_effects.append(effect)

        # 更新效果列表
        self.active_effects = active_effects

# 技能效果类定义
class HealingTreeEffect(pygame.sprite.Sprite):
    def __init__(self, position, image_path, duration):
        super().__init__()
        self.position = pygame.math.Vector2(position)
        self.image_path = image_path
        self.duration = duration
        self.creation_time = pygame.time.get_ticks()

        # 加载图像
        try:
            if image_path and image_path.lower().endswith('.gif'):
                self.frames, self.frame_durations = load_gif_frames(image_path)
                if self.frames:
                    self.image = self.frames[0]
                    self.current_frame = 0
                    self.last_frame_time = self.creation_time
                    self.is_gif = True
                    print(f"成功加载治疗树GIF: {image_path}, 帧数: {len(self.frames)}")
                else:
                    raise Exception("未能加载GIF帧")
            else:
                # 静态图像
                self.image = pygame.image.load(image_path).convert_alpha()
                self.is_gif = False
                print(f"成功加载治疗树静态图: {image_path}")
        except Exception as e:
            print(f"加载治疗树图像失败: {e}")
            # 创建默认图像
            self.image = pygame.Surface((80, 80), pygame.SRCALPHA)
            pygame.draw.circle(self.image, (0, 255, 0, 200), (40, 40), 40)
            self.is_gif = False

        self.rect = self.image.get_rect(center=position)

        print(f"创建治疗树效果: 位置={position}, 持续时间={duration}ms")

    def update(self, dt=16):
        current_time = pygame.time.get_ticks()

        # 更新GIF动画
        if self.is_gif and hasattr(self, 'frames') and self.frames:
            if current_time - self.last_frame_time > 150:  # 每150毫秒更新一帧
                self.current_frame = (self.current_frame + 1) % len(self.frames)
                self.image = self.frames[self.current_frame]
                self.last_frame_time = current_time

                # 更新矩形位置
                old_center = self.rect.center
                self.rect = self.image.get_rect(center=old_center)

        # 检查生命周期
        if current_time - self.creation_time > self.duration:
            self.kill()
            return

class EarthWallEffect(pygame.sprite.Sprite):
    def __init__(self, position, image_path, duration, direction):
        super().__init__()
        self.position = pygame.math.Vector2(position)
        self.image_path = image_path
        self.duration = duration
        self.direction = direction
        self.creation_time = pygame.time.get_ticks()

        # 加载图像
        try:
            if image_path and image_path.lower().endswith('.gif'):
                self.frames, self.frame_durations = load_gif_frames(image_path)
                if self.frames:
                    self.image = self.frames[0]
                    self.current_frame = 0
                    self.last_frame_time = self.creation_time
                    self.is_gif = True
                    print(f"成功加载土墙GIF: {image_path}, 帧数: {len(self.frames)}")
                else:
                    raise Exception("未能加载GIF帧")
            else:
                # 静态图像（带黑色透明处理）
                self.image = load_image_with_transparency(image_path)
                if not self.image:
                    raise Exception("静态图像加载失败")
                self.is_gif = False
                print(f"成功加载土墙静态图: {image_path}, 黑色已透明")
        except Exception as e:
            print(f"加载土墙图像失败: {e}")
            # 创建默认图像
            if direction.x != 0:  # 水平墙
                self.image = pygame.Surface((100, 40), pygame.SRCALPHA)
                pygame.draw.rect(self.image, (139, 69, 19, 200), (0, 0, 100, 40))
            else:  # 垂直墙
                self.image = pygame.Surface((40, 100), pygame.SRCALPHA)
                pygame.draw.rect(self.image, (139, 69, 19, 200), (0, 0, 40, 100))
            self.is_gif = False

        self.rect = self.image.get_rect(center=position)

        print(f"创建土墙视觉效果: 位置={position}, 持续时间={duration}ms")

    def update(self, dt=16):
        current_time = pygame.time.get_ticks()

        # 更新GIF动画
        if self.is_gif and hasattr(self, 'frames') and self.frames:
            if current_time - self.last_frame_time > 200:  # 每200毫秒更新一帧
                self.current_frame = (self.current_frame + 1) % len(self.frames)
                self.image = self.frames[self.current_frame]
                self.last_frame_time = current_time

                # 更新矩形位置
                old_center = self.rect.center
                self.rect = self.image.get_rect(center=old_center)

        # 检查生命周期
        if current_time - self.creation_time > self.duration:
            self.kill()
            return

class MetalSlashEffect(pygame.sprite.Sprite):
    def __init__(self, position, image_path, duration, direction):
        super().__init__()
        self.position = pygame.math.Vector2(position)
        self.image_path = image_path
        self.duration = duration
        self.direction = direction
        self.creation_time = pygame.time.get_ticks()

        # 加载图像
        try:
            if image_path and image_path.lower().endswith('.gif'):
                self.frames, self.frame_durations = load_gif_frames(image_path)
                if self.frames:
                    self.image = self.frames[0]
                    self.current_frame = 0
                    self.last_frame_time = self.creation_time
                    self.is_gif = True
                    print(f"成功加载金属斩击GIF: {image_path}, 帧数: {len(self.frames)}")
                else:
                    raise Exception("未能加载GIF帧")
            else:
                # 静态图像
                self.image = pygame.image.load(image_path).convert_alpha()
                self.is_gif = False
                print(f"成功加载金属斩击静态图: {image_path}")
        except Exception as e:
            print(f"加载金属斩击图像失败: {e}")
            # 创建默认图像
            self.image = pygame.Surface((80, 80), pygame.SRCALPHA)
            pygame.draw.rect(self.image, (255, 215, 0, 200), (0, 0, 80, 80))
            self.is_gif = False

        # 根据方向调整图像
        if direction == "left":
            self.image = pygame.transform.flip(self.image, True, False)
        elif direction == "up":
            self.image = pygame.transform.rotate(self.image, 90)
        elif direction == "down":
            self.image = pygame.transform.rotate(self.image, -90)

        self.rect = self.image.get_rect(center=position)

        print(f"创建金属斩击效果: 位置={position}, 方向={direction}, 持续时间={duration}ms")

    def update(self, dt=16):
        current_time = pygame.time.get_ticks()

        # 更新GIF动画
        if self.is_gif and hasattr(self, 'frames') and self.frames:
            if current_time - self.last_frame_time > 100:  # 每100毫秒更新一帧
                self.current_frame = (self.current_frame + 1) % len(self.frames)
                original_image = self.frames[self.current_frame]

                # 根据方向调整图像
                if self.direction == "left":
                    self.image = pygame.transform.flip(original_image, True, False)
                elif self.direction == "up":
                    self.image = pygame.transform.rotate(original_image, 90)
                elif self.direction == "down":
                    self.image = pygame.transform.rotate(original_image, -90)
                else:
                    self.image = original_image

                self.last_frame_time = current_time

                # 更新矩形位置
                old_center = self.rect.center
                self.rect = self.image.get_rect(center=old_center)

        # 检查生命周期
        if current_time - self.creation_time > self.duration:
            self.kill()
            return

class SpiralBlade(pygame.sprite.Sprite):
    def __init__(self, start_pos, dx, dy, damage, image_path=None):
        super().__init__()
        self.image_path = image_path

        # 加载图像
        try:
            if image_path and image_path.lower().endswith('.gif'):
                self.frames, self.frame_durations = load_gif_frames(image_path)
                self.image = self.frames[0] if self.frames else pygame.Surface((30, 30))
            else:
                self.image = pygame.Surface((30, 30), pygame.SRCALPHA)
                pygame.draw.circle(self.image, (200, 255, 200), (15, 15), 15)
        except Exception as e:
            print(f"加载螺旋剑刃图像失败: {e}")
            self.image = pygame.Surface((30, 30), pygame.SRCALPHA)
            pygame.draw.circle(self.image, (200, 255, 200), (15, 15), 15)

        # 设置位置和速度
        self.rect = self.image.get_rect(center=start_pos)
        self.position = pygame.math.Vector2(start_pos)
        self.velocity = pygame.math.Vector2(dx, dy)

        # 设置伤害和其他属性
        self.damage = damage
        self.creation_time = pygame.time.get_ticks()
        self.lifetime = 2000  # 持续2秒
        self.game = None

        # 动画相关
        self.is_gif = image_path and image_path.lower().endswith('.gif')
        self.current_frame = 0
        self.frame_count = len(self.frames) if hasattr(self, 'frames') and self.frames else 8
        self.frame_duration = 100  # 每帧持续100毫秒
        self.last_frame_time = self.creation_time

    def update(self, walls, enemies, player):
        # 更新位置
        self.position += self.velocity
        self.rect.center = (int(self.position.x), int(self.position.y))

        # 更新动画
        current_time = pygame.time.get_ticks()
        if self.is_gif and current_time - self.last_frame_time > self.frame_duration:
            self.current_frame = (self.current_frame + 1) % self.frame_count
            self.last_frame_time = current_time
            if hasattr(self, 'frames') and self.frames:
                self.image = self.frames[self.current_frame]

        # 检查生命周期
        if current_time - self.creation_time > self.lifetime:
            self.kill()
            return

        # 检查墙体碰撞
        for wall in walls:
            if self.rect.colliderect(wall):
                self.kill()
                return

        # 检查敌人碰撞
        for enemy in enemies:
            if self.rect.colliderect(enemy.rect):
                # 造成伤害
                enemy.hp -= self.damage
                print(f"螺旋剑刃击中敌人! 造成 {self.damage} 点伤害，敌人剩余HP: {enemy.hp}")
                self.kill()
                return

class ShieldEffect(pygame.sprite.Sprite):
    def __init__(self, player, image_path, duration):
        super().__init__()
        self.player = player
        self.image_path = image_path
        self.duration = duration

        # 加载图像
        try:
            if image_path and image_path.lower().endswith('.gif'):
                self.frames, self.frame_durations = load_gif_frames(image_path)
                self.image = self.frames[0] if self.frames else pygame.Surface((80, 80), pygame.SRCALPHA)
            else:
                self.image = pygame.image.load(image_path).convert_alpha() if image_path else pygame.Surface((80, 80), pygame.SRCALPHA)
                self.image = pygame.transform.scale(self.image, (80, 80))
        except Exception as e:
            print(f"加载护盾图像失败: {e}")
            self.image = pygame.Surface((80, 80), pygame.SRCALPHA)
            pygame.draw.circle(self.image, (200, 200, 255, 150), (40, 40), 40)

        # 设置位置
        self.rect = self.image.get_rect(center=player.rect.center)

        # 设置其他属性
        self.creation_time = pygame.time.get_ticks()
        self.is_gif = image_path and image_path.lower().endswith('.gif')
        self.current_frame = 0
        self.frame_count = len(self.frames) if hasattr(self, 'frames') and self.frames else 8
        self.frame_duration = 100  # 每帧持续100毫秒
        self.last_frame_time = self.creation_time

    def update(self):
        # 更新位置（跟随玩家）
        self.rect.center = self.player.rect.center

        # 更新动画
        current_time = pygame.time.get_ticks()
        if self.is_gif and current_time - self.last_frame_time > self.frame_duration:
            self.current_frame = (self.current_frame + 1) % self.frame_count
            self.last_frame_time = current_time
            if hasattr(self, 'frames') and self.frames:
                self.image = self.frames[self.current_frame]

        # 检查生命周期
        if current_time - self.creation_time > self.duration:
            self.kill()

class HealingFlower(pygame.sprite.Sprite):
    def __init__(self, position, image_path, duration):
        super().__init__()
        self.position = position
        self.image_path = image_path
        self.duration = duration

        # 加载图像（带黑色透明处理）
        try:
            if image_path and image_path.lower().endswith('.gif'):
                self.frames, self.frame_durations = load_gif_frames(image_path)
                self.image = self.frames[0] if self.frames else self.create_default_image()
                self.is_gif = True
                print(f"加载治疗花GIF成功，帧数: {len(self.frames) if self.frames else 0}")
            else:
                self.image = load_image_with_transparency(image_path) if image_path else self.create_default_image()
                if self.image:
                    self.image = pygame.transform.scale(self.image, (150, 150))  # 调整尺寸
                else:
                    self.image = self.create_default_image()
                self.is_gif = False
                print(f"加载治疗花静态图成功")
        except Exception as e:
            print(f"加载治疗花图像失败: {e}")
            self.image = self.create_default_image()
            self.is_gif = False

        # 设置位置
        self.rect = self.image.get_rect(center=position)

        # 设置其他属性
        self.creation_time = pygame.time.get_ticks()
        self.current_frame = 0
        self.last_frame_time = self.creation_time

        print(f"治疗花特效创建完成，位置: {position}, 持续时间: {duration}ms")

    def create_default_image(self):
        """创建默认的治疗花图像"""
        image = pygame.Surface((150, 150), pygame.SRCALPHA)
        # 绘制一个绿色的花朵
        pygame.draw.circle(image, (100, 255, 150, 200), (75, 75), 60)
        pygame.draw.circle(image, (150, 255, 200, 150), (75, 75), 40)
        pygame.draw.circle(image, (200, 255, 220, 100), (75, 75), 20)
        return image

    def update(self, dt=16):
        """更新治疗花效果"""
        current_time = pygame.time.get_ticks()

        # 更新GIF动画
        if self.is_gif and hasattr(self, 'frames') and self.frames:
            if hasattr(self, 'frame_durations') and self.frame_durations:
                frame_duration = self.frame_durations[self.current_frame]
            else:
                frame_duration = 100

            if current_time - self.last_frame_time > frame_duration:
                self.current_frame = (self.current_frame + 1) % len(self.frames)
                self.last_frame_time = current_time
                self.image = self.frames[self.current_frame]

        # 检查生命周期
        if current_time - self.creation_time > self.duration:
            print(f"治疗花特效结束（持续{self.duration}ms）")
            self.kill()

class FireTornado(pygame.sprite.Sprite):
    def __init__(self, position, image_path, damage, duration):
        super().__init__()
        self.position = pygame.math.Vector2(position)
        self.image_path = image_path
        self.damage = damage
        self.duration = duration

        # 加载图像
        try:
            if image_path and image_path.lower().endswith('.gif'):
                self.frames, self.frame_durations = load_gif_frames(image_path)
                self.image = self.frames[0] if self.frames else pygame.Surface((120, 120), pygame.SRCALPHA)
            else:
                self.image = pygame.image.load(image_path).convert_alpha() if image_path else pygame.Surface((120, 120), pygame.SRCALPHA)
                self.image = pygame.transform.scale(self.image, (120, 120))
        except Exception as e:
            print(f"加载火焰龙卷图像失败: {e}")
            self.image = pygame.Surface((120, 120), pygame.SRCALPHA)

        # 设置位置
        self.rect = self.image.get_rect(center=position)

        # 设置其他属性
        self.creation_time = pygame.time.get_ticks()
        self.is_gif = image_path and image_path.lower().endswith('.gif')
        self.current_frame = 0
        self.frame_count = len(self.frames) if hasattr(self, 'frames') and self.frames else 8
        self.frame_duration = 100  # 每帧持续100毫秒
        self.last_frame_time = self.creation_time
        self.last_damage_time = self.creation_time
        self.damage_interval = 500  # 每0.5秒造成一次伤害

        # 游戏引用
        self.game = None

    def update(self):
        # 更新动画
        current_time = pygame.time.get_ticks()
        if self.is_gif and current_time - self.last_frame_time > self.frame_duration:
            self.current_frame = (self.current_frame + 1) % self.frame_count
            self.last_frame_time = current_time
            if hasattr(self, 'frames') and self.frames:
                self.image = self.frames[self.current_frame]

        # 持续对范围内敌人造成伤害
        if hasattr(self, 'game') and self.game and hasattr(self.game, 'level_system'):
            if current_time - self.last_damage_time > self.damage_interval:
                enemies_hit = 0
                for enemy in self.game.level_system.enemies[:]:
                    # 计算与敌人的距离
                    enemy_pos = pygame.math.Vector2(enemy.rect.center)
                    distance = self.position.distance_to(enemy_pos)

                    # 如果在范围内（120像素半径），造成伤害
                    if distance < 60:  # 火焰龙卷的伤害范围
                        damage_per_tick = self.damage // 5  # 每次造成总伤害的1/5
                        enemy.hp -= damage_per_tick
                        enemies_hit += 1
                        print(f"火焰龙卷击中敌人! 造成 {damage_per_tick} 点伤害，敌人剩余HP: {enemy.hp}")

                        # 添加受伤特效
                        if hasattr(self.game, 'combat_system'):
                            self.game.combat_system.add_damage_effect(enemy.rect.center, damage_per_tick)

                if enemies_hit > 0:
                    print(f"火焰龙卷共击中 {enemies_hit} 个敌人")
                self.last_damage_time = current_time

        # 检查生命周期
        if current_time - self.creation_time > self.duration:
            print(f"火焰龙卷效果结束")
            self.kill()

    def apply_damage(self):
        if not hasattr(self, 'game') or not self.game:
            return

        for enemy in self.game.level_system.enemies:
            # 计算与敌人的距离
            enemy_pos = pygame.math.Vector2(enemy.rect.center)
            distance = self.position.distance_to(enemy_pos)

            # 如果在范围内，造成伤害
            enemy.hp -= self.damage / 10  # 每次造成总伤害的1/10
            print(f"火焰龙卷击中敌人! 造成 {self.damage/10} 点伤害，敌人剩余HP: {enemy.hp}")

class BurningFlameEffect(pygame.sprite.Sprite):
    """焚天烈焰范围攻击特效类"""
    def __init__(self, position, image_path, damage, radius, duration):
        super().__init__()
        self.position = pygame.math.Vector2(position)
        self.image_path = image_path
        self.damage = damage
        self.radius = radius
        self.duration = duration
        self.creation_time = pygame.time.get_ticks()

        # 加载图像
        try:
            if image_path and image_path.lower().endswith('.gif'):
                self.frames, self.frame_durations = load_gif_frames(image_path)
                if self.frames:
                    self.image = self.frames[0]
                    self.current_frame = 0
                    self.last_frame_time = self.creation_time
                    self.is_gif = True
                    print(f"成功加载焚天烈焰GIF: {image_path}, 帧数: {len(self.frames)}")
                else:
                    raise Exception("未能加载GIF帧")
            else:
                # 静态图像（带黑色透明处理）
                if image_path:
                    self.image = load_image_with_transparency(image_path)
                    if self.image:
                        # 根据攻击范围调整图像大小
                        size = int(radius * 2)
                        self.image = pygame.transform.scale(self.image, (size, size))
                        print(f"成功加载焚天烈焰静态图: {image_path}, 黑色已透明")
                    else:
                        raise Exception("静态图像加载失败")
                else:
                    raise Exception("图像路径为空")
                self.is_gif = False
        except Exception as e:
            print(f"加载焚天烈焰图像失败: {e}，使用默认图像")
            # 创建一个默认的火焰效果
            size = int(radius * 2)
            self.image = pygame.Surface((size, size), pygame.SRCALPHA)
            # 绘制火焰效果
            pygame.draw.circle(self.image, (255, 100, 0, 200), (size//2, size//2), radius)
            pygame.draw.circle(self.image, (255, 200, 0, 150), (size//2, size//2), radius//2)
            self.is_gif = False

        # 设置rect
        self.rect = self.image.get_rect()
        self.rect.center = position

        # 游戏引用
        self.game = None

    def update(self, dt=16):
        """更新焚天烈焰效果"""
        current_time = pygame.time.get_ticks()

        # 更新GIF动画
        if self.is_gif and hasattr(self, 'frames') and self.frames:
            if hasattr(self, 'frame_durations') and self.frame_durations:
                frame_duration = self.frame_durations[self.current_frame]
            else:
                frame_duration = 100

            if current_time - self.last_frame_time > frame_duration:
                self.current_frame = (self.current_frame + 1) % len(self.frames)
                self.last_frame_time = current_time
                self.image = self.frames[self.current_frame]

        # 检查生命周期
        if current_time - self.creation_time > self.duration:
            print(f"焚天烈焰效果结束")
            self.kill()
            return

class SteamExplosion(pygame.sprite.Sprite):
    def __init__(self, position, image_path, damage, radius):
        super().__init__()
        self.position = pygame.math.Vector2(position)
        self.image_path = image_path
        self.damage = damage
        self.radius = radius
        self.creation_time = pygame.time.get_ticks()  # 添加初始化

        # 加载图像
        try:
            if image_path and image_path.lower().endswith('.gif'):
                self.frames, self.frame_durations = load_gif_frames(image_path)
                self.image = self.frames[0] if self.frames else pygame.Surface((2*radius, 2*radius), pygame.SRCALPHA)
            else:
                self.image = pygame.image.load(image_path).convert_alpha() if image_path else pygame.Surface((2*radius, 2*radius), pygame.SRCALPHA)
                self.image = pygame.transform.scale(self.image, (2*radius, 2*radius))
        except Exception as e:
            print(f"加载蒸汽爆炸图像失败: {e}")
            self.image = pygame.Surface((2*radius, 2*radius), pygame.SRCALPHA)
            pygame.draw.circle(self.image, (200, 200, 255, 150), (radius, radius), radius)

        # 设置位置
        self.rect = self.image.get_rect(center=position)

        # 设置其他属性
        self.creation_time = pygame.time.get_ticks()
        self.duration = 1000  # 爆炸持续1秒
        self.game = None

        # 动画相关
        self.is_gif = image_path and image_path.lower().endswith('.gif')
        self.frame_count = len(self.frames) if hasattr(self, 'frames') and self.frames else 8
        self.last_frame_time = self.creation_time


    def update(self):
        # 更新动画
        current_time = pygame.time.get_ticks()
        self.current_frame = (self.current_frame + 1) % self.frame_count
        self.last_frame_time = current_time

        # 检查生命周期
        if current_time - self.creation_time > self.duration:
            self.kill()
            return

    def apply_damage(self):
        """对范围内的敌人造成伤害"""
        if not hasattr(self, 'game') or not self.game:
            return

        for enemy in self.game.level_system.enemies:
            # 计算与敌人的距离
            enemy_pos = pygame.math.Vector2(enemy.rect.center)
            distance = self.position.distance_to(enemy_pos)

            # 如果在范围内，造成伤害
            if distance < self.radius:
                # 距离越近伤害越高
                damage_factor = 1 - (distance / self.radius)
                actual_damage = self.damage * damage_factor

                # 对敌人造成伤害
                enemy.hp -= actual_damage
                print(f"蒸汽爆炸击中敌人! 造成 {actual_damage:.2f} 点伤害，敌人剩余HP: {enemy.hp}")

                # 添加受伤特效
                if hasattr(self.game, 'combat_system'):
                    self.game.combat_system.add_damage_effect(enemy.rect.center, actual_damage)

class TreeGuardian(pygame.sprite.Sprite):
    def __init__(self, position, image_path, duration):
        super().__init__()
        self.position = pygame.math.Vector2(position)
        self.image_path = image_path
        self.duration = duration

        # 加载图像
        try:
            if image_path and image_path.lower().endswith('.gif'):
                self.frames, self.frame_durations = load_gif_frames(image_path)
                self.image = self.frames[0] if self.frames else pygame.Surface((120, 180), pygame.SRCALPHA)
            else:
                self.image = pygame.image.load(image_path).convert_alpha() if image_path else pygame.Surface((120, 180), pygame.SRCALPHA)
                self.image = pygame.transform.scale(self.image, (120, 180))
        except Exception as e:
            print(f"加载树人守卫图像失败: {e}")
            self.image = pygame.Surface((120, 180), pygame.SRCALPHA)

        # 设置位置
        self.rect = self.image.get_rect(center=position)

        # 设置其他属性
        self.creation_time = pygame.time.get_ticks()
        self.game = None
        self.hp = 100  # 树人生命值
        self.aggro_radius = 300  # 吸引敌人的范围

        # 动画相关
        self.is_gif = image_path and image_path.lower().endswith('.gif')
        self.current_frame = 0
        self.frame_count = len(self.frames) if hasattr(self, 'frames') and self.frames else 8
        self.last_frame_time = self.creation_time

    def update(self):
        # 更新动画
        current_time = pygame.time.get_ticks()
        if self.is_gif and current_time - self.last_frame_time > self.frame_duration:
            self.current_frame = (self.current_frame + 1) % self.frame_count
            self.last_frame_time = current_time
            if hasattr(self, 'frames') and self.frames:
                self.image = self.frames[self.current_frame]

        # 吸引敌人
        self.attract_enemies()

        # 检查生命周期
        if current_time - self.creation_time > self.duration or self.hp <= 0:
            self.kill()

    def attract_enemies(self):
        """吸引范围内的敌人"""
        if not hasattr(self, 'game') or not self.game:
            return

        for enemy in self.game.level_system.enemies:
            # 计算与敌人的距离
            enemy_pos = pygame.math.Vector2(enemy.rect.center)
            distance = self.position.distance_to(enemy_pos)

            # 如果在范围内，吸引敌人
            if distance < self.aggro_radius:
                # 改变敌人的目标为树人
                if hasattr(enemy, 'target'):
                    enemy.target = self

                # 或者直接修改敌人的移动方向
                direction = self.position - enemy_pos
                if direction.length() > 0:
                    direction.normalize_ip()
                    enemy.direction = direction

class SwampTrap(pygame.sprite.Sprite):
    def __init__(self, position, image_path, duration):
        super().__init__()
        self.image_path = image_path
        self.duration = duration

        # 加载图像
        try:
            if image_path and image_path.lower().endswith('.gif'):
                self.frames, self.frame_durations = load_gif_frames(image_path)
                self.image = self.frames[0] if self.frames else pygame.Surface((150, 150), pygame.SRCALPHA)
            else:
                self.image = pygame.image.load(image_path).convert_alpha() if image_path else pygame.Surface((150, 150), pygame.SRCALPHA)
                self.image = pygame.transform.scale(self.image, (150, 150))
        except Exception as e:
            print(f"加载沼泽陷阱图像失败: {e}")
            self.image = pygame.Surface((150, 150), pygame.SRCALPHA)
            pygame.draw.circle(self.image, (100, 150, 50, 150), (75, 75), 75)

        # 设置位置
        self.rect = self.image.get_rect(center=position)

        # 设置其他属性
        self.game = None
        self.slow_factor = COMBO_SKILLS["water_earth"]["slow_factor"]  # 减速因子
        self.damage = COMBO_SKILLS["water_earth"]["damage"]  # 伤害值
        self.creation_time = pygame.time.get_ticks()  # 添加初始化
        self.last_damage_time = self.creation_time
        self.damage_interval = 1000  # 每秒造成一次伤害

        # 动画相关
        self.is_gif = image_path and image_path.lower().endswith('.gif')
        self.current_frame = 0
        self.frame_count = len(self.frames) if hasattr(self, 'frames') and self.frames else 8
        self.frame_duration = 100  # 每帧持续100毫秒
        self.last_frame_time = self.creation_time

    def update(self):
        # 更新动画
        current_time = pygame.time.get_ticks()
        if self.is_gif and current_time - self.last_frame_time > self.frame_duration:
            self.current_frame = (self.current_frame + 1) % self.frame_count
            self.last_frame_time = current_time
            if hasattr(self, 'frames') and self.frames:
                self.image = self.frames[self.current_frame]

        # 应用减速效果
        self.apply_slow()

        # 检查生命周期
        if current_time - self.creation_time > self.duration:
            self.kill()

    def apply_slow(self):
        """对范围内的敌人应用减速效果和伤害"""
        if not hasattr(self, 'game') or not self.game:
            return

        current_time = pygame.time.get_ticks()
        # 创建一个集合来跟踪当前在沼泽中的敌人
        enemies_in_swamp = set()

        for enemy in self.game.level_system.enemies:
            # 计算与敌人的距离
            enemy_pos = pygame.math.Vector2(enemy.rect.center)
            distance = self.position.distance_to(enemy_pos)

            # 检查敌人是否在沼泽范围内
            in_range = self.rect.colliderect(enemy.rect)

            if in_range:
                # 将敌人添加到当前在沼泽中的集合
                enemies_in_swamp.add(enemy)

                # 如果敌人还没有减速，应用减速效果
                if not hasattr(enemy, 'original_speed'):
                    enemy.original_speed = enemy.speed
                    enemy.in_swamp = True
                    enemy.speed = enemy.original_speed * self.slow_factor
                    print(f"敌人进入沼泽，速度减慢至 {enemy.speed:.1f}")

                # 定期造成伤害
                if current_time - self.last_damage_time > self.damage_interval:
                    enemy.hp -= self.damage
                    print(f"沼泽陷阱对敌人造成 {self.damage} 点伤害，敌人剩余HP: {enemy.hp}")

                    # 添加受伤特效
                    if hasattr(self.game, 'combat_system'):
                        self.game.combat_system.add_damage_effect(enemy.rect.center, self.damage)

        # 更新伤害时间
        if current_time - self.last_damage_time > self.damage_interval:
            self.last_damage_time = current_time

        # 检查之前在沼泽中但现在不在的敌人，恢复他们的速度
        for enemy in self.game.level_system.enemies:
            if enemy not in enemies_in_swamp and hasattr(enemy, 'in_swamp') and enemy.in_swamp:
                enemy.speed = enemy.original_speed
                enemy.in_swamp = False
                print(f"敌人离开沼泽，速度恢复至 {enemy.speed:.1f}")

class HeavenlySword(pygame.sprite.Sprite):
    def __init__(self, position, image_path, damage, radius):
        super().__init__()
        self.position = pygame.math.Vector2(position)
        self.image_path = image_path
        self.damage = damage
        self.radius = radius
        self.creation_time = pygame.time.get_ticks()
        self.lifetime = 1400  # 持续1.4秒

        # 加载图像
        try:
            if image_path and image_path.lower().endswith('.gif'):
                self.frames, self.frame_durations = load_gif_frames(image_path)
                if self.frames:
                    self.image = self.frames[0]
                    self.current_frame = 0
                    self.last_frame_time = self.creation_time
                    self.is_gif = True
                    print(f"成功加载天降神剑GIF: {image_path}, 帧数: {len(self.frames)}")
                else:
                    raise Exception("未能加载GIF帧")
            else:
                # 静态图像
                if image_path:
                    self.image = pygame.image.load(image_path).convert_alpha()
                    self.image = pygame.transform.scale(self.image, (150, 300))
                    print(f"成功加载天降神剑静态图: {image_path}")
                else:
                    raise Exception("图像路径为空")
                self.is_gif = False
        except Exception as e:
            print(f"加载天降神剑图像失败: {e}，使用默认图像")
            # 创建一个更大更明显的默认图像
            self.image = pygame.Surface((150, 300), pygame.SRCALPHA)
            # 绘制一个金色的剑形
            pygame.draw.rect(self.image, (255, 215, 0, 255), (70, 0, 10, 250))  # 剑身
            pygame.draw.polygon(self.image, (255, 215, 0, 255), [(75, 0), (60, 30), (90, 30)])  # 剑尖
            pygame.draw.rect(self.image, (139, 69, 19, 255), (65, 250, 20, 30))  # 剑柄
            pygame.draw.rect(self.image, (255, 215, 0, 255), (50, 240, 50, 10))  # 护手
            self.is_gif = False

        # 设置rect
        self.rect = self.image.get_rect()
        self.rect.center = position

        # 游戏引用
        self.game = None

        # 设置位置
        self.rect = self.image.get_rect(center=position)

        # 初始化速度向量（默认为静止）
        self.velocity = pygame.math.Vector2(0, 0)

        # 设置其他属性
        self.creation_time = pygame.time.get_ticks()
        self.lifetime = 1400  # 持续1.4秒
        self.game = None
        self.frame_duration = 100  # 添加帧持续时间初始化

        # 动画相关
        self.is_gif = image_path and image_path.lower().endswith('.gif')
        self.current_frame = 0
        self.frame_count = len(self.frames) if hasattr(self, 'frames') and self.frames else 8
        self.last_frame_time = self.creation_time

        # 伤害间隔
        self.last_damage_time = self.creation_time
        self.damage_interval = 200  # 每0.2秒造成一次伤害

    def update(self, dt=16):
        """更新天降神剑效果"""
        current_time = pygame.time.get_ticks()

        # 更新位置（如果有速度）
        if hasattr(self, 'velocity'):
            self.position += self.velocity
            self.rect.center = self.position

        # 更新GIF动画
        if self.is_gif and hasattr(self, 'frames') and self.frames:
            if hasattr(self, 'frame_durations') and self.frame_durations:
                frame_duration = self.frame_durations[self.current_frame]
            else:
                frame_duration = 100

            if current_time - self.last_frame_time > frame_duration:
                self.current_frame = (self.current_frame + 1) % len(self.frames)
                self.last_frame_time = current_time
                self.image = self.frames[self.current_frame]

        # 检查是否到达伤害时间（1.4秒）
        if not hasattr(self, 'damage_applied') and current_time - self.creation_time >= self.lifetime:
            # 在1.4秒后对敌人造成伤害
            self.apply_damage_to_all_enemies()
            self.damage_applied = True
            print(f"天降神兵在{self.lifetime}ms后对敌人造成伤害")

        # 检查生命周期（伤害后立即消失）
        if hasattr(self, 'damage_applied') and self.damage_applied:
            print(f"天降神剑效果结束")
            self.kill()
            return

    def apply_damage_to_all_enemies(self):
        """对全屏所有敌人造成伤害（模仿绝对零度）"""
        if not hasattr(self, 'game') or not self.game:
            return

        enemies_hit = 0
        for enemy in self.game.level_system.enemies:
            # 直接修改敌人HP（模仿绝对零度的方式）
            enemy.hp -= self.damage
            enemies_hit += 1
            print(f"天降神兵击中敌人! 造成 {self.damage} 点伤害，敌人剩余HP: {enemy.hp}")

            # 添加红色粒子效果
            if hasattr(self.game, 'combat_system'):
                self.game.combat_system.add_damage_effect(enemy.rect.center, self.damage)

        print(f"天降神兵共击中 {enemies_hit} 个敌人")

    def apply_damage(self):
        """对范围内的敌人造成伤害（保留旧方法）"""
        if not hasattr(self, 'game') or not self.game:
            return

        for enemy in self.game.level_system.enemies:
            # 计算与敌人的距离
            enemy_pos = pygame.math.Vector2(enemy.rect.center)
            distance = self.position.distance_to(enemy_pos)

            # 如果在范围内，造成伤害
            if distance < self.radius:
                # 对敌人造成伤害
                enemy.hp -= self.damage
                print(f"天降神兵击中敌人! 造成 {self.damage} 点伤害，敌人剩余HP: {enemy.hp}")

                # 添加受伤特效
                if hasattr(self.game, 'combat_system'):
                    self.game.combat_system.add_damage_effect(enemy.rect.center, self.damage)

class TreeGuardian(pygame.sprite.Sprite):
    def __init__(self, position, image_path, duration):
        super().__init__()
        self.position = pygame.math.Vector2(position)
        self.image_path = image_path
        self.duration = duration

        # 加载图像
        try:
            if image_path and image_path.lower().endswith('.gif'):
                self.frames, self.frame_durations = load_gif_frames(image_path)
                self.image = self.frames[0] if self.frames else pygame.Surface((120, 180), pygame.SRCALPHA)
            else:
                self.image = pygame.image.load(image_path).convert_alpha() if image_path else pygame.Surface((120, 180), pygame.SRCALPHA)
                self.image = pygame.transform.scale(self.image, (120, 180))
        except Exception as e:
            print(f"加载树人守卫图像失败: {e}")
            self.image = pygame.Surface((120, 180), pygame.SRCALPHA)
            pygame.draw.rect(self.image, (50, 150, 50, 200), (0, 0, 120, 180))

        # 设置位置
        self.rect = self.image.get_rect(center=position)

        # 设置其他属性
        self.creation_time = pygame.time.get_ticks()
        self.game = None
        self.hp = 100  # 树人生命值
        self.aggro_radius = 300  # 吸引敌人的范围

        # 动画相关
        self.is_gif = image_path and image_path.lower().endswith('.gif')
        self.current_frame = 0
        self.frame_count = len(self.frames) if hasattr(self, 'frames') and self.frames else 8
        self.frame_duration = 100  # 每帧持续100毫秒
        self.last_frame_time = self.creation_time

    def update(self):
        # 更新动画
        current_time = pygame.time.get_ticks()
        if self.is_gif and current_time - self.last_frame_time > self.frame_duration:
            self.current_frame = (self.current_frame + 1) % self.frame_count
            self.last_frame_time = current_time
            if hasattr(self, 'frames') and self.frames:
                self.image = self.frames[self.current_frame]

        # 吸引敌人
        self.attract_enemies()

        # 检查生命周期
        if current_time - self.creation_time > self.duration or self.hp <= 0:
            self.kill()

    def attract_enemies(self):
        """吸引范围内的敌人"""
        if not hasattr(self, 'game') or not self.game:
            return

        for enemy in self.game.level_system.enemies:
            # 计算与敌人的距离
            enemy_pos = pygame.math.Vector2(enemy.rect.center)
            distance = self.position.distance_to(enemy_pos)

            # 如果在范围内，吸引敌人
            if distance < self.aggro_radius:
                # 改变敌人的目标为树人
                if hasattr(enemy, 'target'):
                    enemy.target = self

                # 或者直接修改敌人的移动方向
                direction = self.position - enemy_pos
                if direction.length() > 0:
                    direction.normalize_ip()
                    enemy.direction = direction

class FreezeEffect(pygame.sprite.Sprite):
    def __init__(self, position, image_path, duration):
        super().__init__()
        self.position = pygame.math.Vector2(position)
        self.image_path = image_path
        self.duration = duration

        # 加载图像
        try:
            if image_path and image_path.lower().endswith('.gif'):
                self.frames, self.frame_durations = load_gif_frames(image_path)
                self.image = self.frames[0] if self.frames else pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
            else:
                self.image = pygame.image.load(image_path).convert_alpha() if image_path else pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
                self.image = pygame.transform.scale(self.image, (SCREEN_WIDTH, SCREEN_HEIGHT))
        except Exception as e:
            print(f"加载冰冻效果图像失败: {e}")
            self.image = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
            self.image.fill((100, 200, 255, 50))

        # 设置位置
        self.rect = self.image.get_rect(center=position)

        # 设置其他属性
        self.creation_time = pygame.time.get_ticks()
        self.game = None

        # 动画相关
        self.is_gif = image_path and image_path.lower().endswith('.gif')
        self.current_frame = 0
        self.frame_count = len(self.frames) if hasattr(self, 'frames') and self.frames else 8
        self.frame_duration = 100  # 每帧持续100毫秒
        self.last_frame_time = self.creation_time

    def update(self):
        # 更新动画
        current_time = pygame.time.get_ticks()
        if self.is_gif and current_time - self.last_frame_time > self.frame_duration:
            self.current_frame = (self.current_frame + 1) % self.frame_count
            self.last_frame_time = current_time
            if hasattr(self, 'frames') and self.frames:
                self.image = self.frames[self.current_frame]

        # 检查生命周期
        if current_time - self.creation_time > self.duration:
            self.kill()

class FireDragon(pygame.sprite.Sprite):
    def __init__(self, start_pos, image_path, duration):
        super().__init__()
        self.position = pygame.math.Vector2(start_pos)
        self.image_path = image_path
        self.duration = duration
        self.creation_time = pygame.time.get_ticks()
        self.game = None

        # 加载图像（带黑色透明处理）
        try:
            if image_path and image_path.lower().endswith('.gif'):
                self.frames, self.frame_durations = load_gif_frames(image_path)
                self.image = self.frames[0] if self.frames else self.create_default_image()
                self.is_gif = True
                print(f"加载火龙GIF成功，帧数: {len(self.frames) if self.frames else 0}")
            else:
                self.image = load_image_with_transparency(image_path) if image_path else self.create_default_image()
                if self.image:
                    self.image = pygame.transform.scale(self.image, (300, 200))  # 放大尺寸
                else:
                    self.image = self.create_default_image()
                self.is_gif = False
                print(f"加载火龙静态图成功")
        except Exception as e:
            print(f"加载火龙图像失败: {e}")
            self.image = self.create_default_image()
            self.is_gif = False

        # 设置位置
        self.rect = self.image.get_rect(center=start_pos)

        # 动画相关
        self.current_frame = 0
        self.last_frame_time = self.creation_time

        print(f"火龙特效创建完成，位置: {start_pos}, 持续时间: {duration}ms")

    def create_default_image(self):
        """创建默认的火龙图像"""
        image = pygame.Surface((300, 200), pygame.SRCALPHA)
        # 绘制一个红色的火龙形状
        pygame.draw.ellipse(image, (255, 100, 0, 200), (0, 0, 300, 200))
        pygame.draw.ellipse(image, (255, 200, 0, 150), (50, 50, 200, 100))
        return image

    def update(self, dt=16):
        """更新火龙效果（只显示特效，不造成伤害）"""
        current_time = pygame.time.get_ticks()

        # 更新GIF动画
        if self.is_gif and hasattr(self, 'frames') and self.frames:
            if hasattr(self, 'frame_durations') and self.frame_durations:
                frame_duration = self.frame_durations[self.current_frame]
            else:
                frame_duration = 100

            if current_time - self.last_frame_time > frame_duration:
                self.current_frame = (self.current_frame + 1) % len(self.frames)
                self.last_frame_time = current_time
                self.image = self.frames[self.current_frame]

        # 检查生命周期
        if current_time - self.creation_time > self.duration:
            print(f"火龙特效结束（持续{self.duration}ms）")
            self.kill()

        # 火龙术只显示特效，不在这里造成伤害
        # 伤害已经在fire_dragon方法中立即应用了
        pass

class EarthquakeEffect(pygame.sprite.Sprite):
    def __init__(self, position, image_path, damage, duration):
        super().__init__()
        self.position = pygame.math.Vector2(position)
        self.image_path = image_path
        self.damage = damage
        self.duration = duration
        self.creation_time = pygame.time.get_ticks()
        self.damage_applied = False  # 标记是否已经造成伤害

        # 加载图像
        try:
            if image_path and image_path.lower().endswith('.gif'):
                self.frames, self.frame_durations = load_gif_frames(image_path)
                if self.frames:
                    self.image = self.frames[0]
                    self.current_frame = 0
                    self.last_frame_time = self.creation_time
                    self.is_gif = True
                    print(f"成功加载地震GIF: {image_path}, 帧数: {len(self.frames)}")
                else:
                    raise Exception("未能加载GIF帧")
            else:
                # 静态图像
                if image_path:
                    self.image = pygame.image.load(image_path).convert_alpha()
                    self.image = pygame.transform.scale(self.image, (SCREEN_WIDTH, SCREEN_HEIGHT))
                    print(f"成功加载地震静态图: {image_path}")
                else:
                    raise Exception("图像路径为空")
                self.is_gif = False
        except Exception as e:
            print(f"加载地震图像失败: {e}，使用默认图像")
            # 创建一个全屏的默认地震效果
            self.image = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
            # 绘制地面裂纹效果
            for i in range(10):
                start_x = i * (SCREEN_WIDTH // 10)
                pygame.draw.line(self.image, (139, 69, 19, 200),
                               (start_x, SCREEN_HEIGHT - 50),
                               (start_x + SCREEN_WIDTH // 10, SCREEN_HEIGHT - 30), 5)
            self.is_gif = False

        # 设置rect
        self.rect = self.image.get_rect()
        self.rect.center = position

        # 游戏引用
        self.game = None

        # 伤害间隔
        self.last_damage_time = self.creation_time
        self.damage_interval = 1000  # 每1秒造成一次伤害

    def update(self, dt=16):
        """更新地震效果（动画结束后造成伤害）"""
        current_time = pygame.time.get_ticks()

        # 更新GIF动画
        if self.is_gif and hasattr(self, 'frames') and self.frames:
            if hasattr(self, 'frame_durations') and self.frame_durations:
                frame_duration = self.frame_durations[self.current_frame]
            else:
                frame_duration = 100

            if current_time - self.last_frame_time > frame_duration:
                self.current_frame = (self.current_frame + 1) % len(self.frames)
                self.last_frame_time = current_time
                self.image = self.frames[self.current_frame]

        # 检查是否到达伤害时间（0.9秒）
        if not self.damage_applied and current_time - self.creation_time >= self.duration:
            # 0.9秒后对敌人造成伤害
            self.apply_damage_to_all_enemies()
            self.damage_applied = True
            print(f"地震术在{self.duration}ms后对敌人造成伤害")

        # 检查生命周期（伤害后立即消失）
        if self.damage_applied:
            print(f"地震效果结束")
            self.kill()

    def apply_damage_to_all_enemies(self):
        """对全屏所有敌人造成伤害"""
        if not hasattr(self, 'game') or not self.game:
            return

        enemies_hit = 0
        for enemy in self.game.level_system.enemies:
            # 直接修改敌人HP
            enemy.hp -= self.damage
            enemies_hit += 1
            print(f"地震术击中敌人! 造成 {self.damage} 点伤害，敌人剩余HP: {enemy.hp}")

            # 添加红色粒子效果
            if hasattr(self.game, 'combat_system'):
                self.game.combat_system.add_damage_effect(enemy.rect.center, self.damage)

        print(f"地震术共击中 {enemies_hit} 个敌人")





































