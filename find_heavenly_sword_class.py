#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查找HeavenlySword类定义
"""

def find_heavenly_sword_class():
    """查找HeavenlySword类定义"""
    try:
        with open('combat_system.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print("搜索HeavenlySword类定义...")
        
        for i, line in enumerate(lines, 1):
            if 'class HeavenlySword' in line:
                print(f"找到HeavenlySword类在第{i}行")
                
                # 显示类定义和初始化方法
                start = max(0, i-1)
                end = min(len(lines), i+50)
                print(f"\nHeavenlySword类定义 (第{start+1}-{end}行):")
                for j in range(start, end):
                    marker = ">>> " if j == i-1 else "    "
                    print(f"{marker}{j+1:4d}: {lines[j].rstrip()}")
                    
                    # 如果遇到下一个类定义就停止
                    if j > i and lines[j].strip().startswith('class ') and 'HeavenlySword' not in lines[j]:
                        break
                print()
                break
        
    except Exception as e:
        print(f"搜索失败: {e}")

if __name__ == "__main__":
    find_heavenly_sword_class()
