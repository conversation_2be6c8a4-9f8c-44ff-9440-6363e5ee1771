#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查找fire_projectile方法
"""

def find_fire_projectile_method():
    """查找fire_projectile方法"""
    try:
        with open('enemies.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print("搜索fire_projectile方法...")
        
        for i, line in enumerate(lines, 1):
            if 'def fire_projectile' in line:
                print(f"找到fire_projectile方法在第{i}行")
                
                # 显示方法内容
                start = max(0, i-1)
                end = min(len(lines), i+20)
                print(f"\n方法内容 (第{start+1}-{end}行):")
                for j in range(start, end):
                    marker = ">>> " if j == i-1 else "    "
                    print(f"{marker}{j+1:4d}: {lines[j].rstrip()}")
                print()
        
    except Exception as e:
        print(f"搜索失败: {e}")

if __name__ == "__main__":
    find_fire_projectile_method()
