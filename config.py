# config.py
import pygame

# 窗口设置
SCREEN_WIDTH = 1320
SCREEN_HEIGHT = 800
FPS =40

# 颜色定义
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
BLUE = (0, 0, 255)
GREEN = (0, 255, 0)
YELLOW = (255, 255, 0)
BROWN = (139, 69, 19)

# 元素颜色
ELEMENT_COLORS = {
    "metal": (255, 215, 0),  # 金色
    "wood": (34, 139, 34),   # 绿色
    "water": (30, 144, 255), # 蓝色
    "fire": (255, 69, 0),    # 红色
    "earth": (160, 82, 45)   # 棕色
}

# 资源路径
IMAGE_PATHS = {
    "player": "assets/images/player_sprite.png",
    "melee_enemy": "assets/images/melee_enemy.gif",
    "ranged_enemy": "assets/images/ranged_enemy.gif",
    "background": "assets/background/ground.png",
    "portal": "assets/images/portal.png",  # 添加传送门图像路径
    # 基础技能图标
    "fire_icon": "assets/skills/icons/fire_icon.png",
    "water_icon": "assets/skills/icons/water_icon.png",
    "earth_icon": "assets/skills/icons/earth_icon.png",
    "wood_icon": "assets/skills/icons/wood_icon.png",
    "metal_icon": "assets/skills/icons/metal_icon.png",
    # 组合技能图标
    "metal_wood_icon": "assets/skills/icons/metal_wood_icon.png",
    "metal_earth_icon": "assets/skills/icons/metal_earth_icon.png",
    "metal_metal_icon": "assets/skills/icons/metal_metal_icon.png",
    "wood_wood_icon": "assets/skills/icons/wood_wood_icon.png",
    "fire_fire_icon": "assets/skills/icons/fire_fire_icon.png",
    "water_water_icon": "assets/skills/icons/water_water_icon.png",
    "earth_earth_icon": "assets/skills/icons/earth_earth_icon.png",
    # 缺失的组合技能图标
    "wood_water_icon": "assets/skills/icons/wood_water_icon.png",
    "wood_fire_icon": "assets/skills/icons/wood_fire_icon.png",
    "water_fire_icon": "assets/skills/icons/water_fire_icon.png",
    "water_earth_icon": "assets/skills/icons/water_earth_icon.png",
    # 技能效果
    "fire_effect": "assets/skills/effects/fire_effect.png",  # 改为PNG格式
    "water_effect": "assets/skills/effects/water_effect.gif",
    "earth_effect": "assets/skills/effects/earth_wall.png",
    "wood_effect": "assets/skills/effects/healing_tree.png",
    "metal_effect": "assets/skills/effects/metal_slash.gif"
}

# 玩家初始属性
PLAYER_STATS = {
    "hp": 100,
    "mp": 100,
    "speed": 6,
    "mp_regen": 5,  # 每秒MP恢复量
    "collision_offset": 5
}

# 敌人初始属性
ENEMY_STATS = {
    "melee": {
        "hp": 20,
        "speed": 3,
        "attack_damage": 10,
        "attack_range": 50,
        "attack_interval": 2000  # 攻击间隔（毫秒）
    },
    "ranged": {
        "hp": 20,
        "speed": 3,
        "attack_damage": 8,
        "attack_range": 300,
        "attack_interval": 3000  # 攻击间隔（毫秒）
    }
}

# 基础技能配置
BASE_SKILLS = {
    "metal": {
        "name": "Metal Slash",
        "name_cn": "金属斩击",
        "cost": 0,
        "cooldown": 1000,
        "damage": 19,
        "key": pygame.K_1,
        "icon": "metal_icon",
        "effect": "metal_effect",
        "description": "A powerful melee attack"
    },
    "wood": {
        "name": "Wood Healing",
        "name_cn": "木系治疗术",
        "cost": 20,
        "cooldown": 10000,
        "healing": 20,
        "heal_rate": 4,  # 每秒回血量
        "key": pygame.K_2,
        "icon": "wood_icon",
        "effect": "wood_effect",
        "description": "Heals HP over time"
    },
    "water": {
        "name": "Water Wave",
        "name_cn": "水滴攻击",
        "cost": 10,
        "cooldown": 2000,
        "damage": 15,
        "speed": 5,  # 添加速度参数
        "key": pygame.K_3,
        "icon": "water_icon",
        "effect": "water_effect",
        "description": "Area water attack"
    },
    "fire": {
        "name": "Fireball",
        "name_cn": "火球术",
        "cost": 15,
        "cooldown": 2500,
        "damage": 18,
        "speed": 7,  # 添加速度参数
        "key": pygame.K_4,
        "icon": "fire_icon",
        "effect": "fire_effect",
        "description": "Projectile fire attack"
    },
    "earth": {
        "name": "Earth Wall",
        "name_cn": "土墙术",
        "cost": 25,
        "cooldown": 8000,
        "duration": 10000,  # 持续时间（毫秒）
        "key": pygame.K_5,
        "icon": "earth_icon",
        "effect": "earth_effect",
        "description": "Creates a temporary wall"
    }
}

# 组合技能配置
COMBO_SKILLS = {
    "metal_wood": {
        "name": "Wind Blade",
        "name_cn": "疾风剑刃",
        "cost": 30,
        "cooldown": 15000,
        "damage": 25,
        "icon": "metal_wood_icon",
        "effect": "assets/skills/effects/wind_blade.gif",
        "description": "Fast spinning blade attack"
    },
    "metal_earth": {
        "name": "Rock Shield",
        "name_cn": "坚岩护盾",
        "cost": 35,
        "cooldown": 20000,
        "shield": 20,
        "duration": 15000,
        "icon": "metal_earth_icon",
        "effect": "assets/skills/effects/rock_shield.gif",
        "description": "Protective earth barrier"
    },
    "metal_metal": {
        "name": "Divine Sword",
        "name_cn": "天降神兵",
        "cost": 40,
        "cooldown": 25000,
        "damage": 50,
        "icon": "metal_metal_icon",
        "effect": "assets/skills/effects/divine_sword.gif",
        "description": "Full screen heavenly sword strike"
    },
    "wood_wood": {
        "name": "Tree Guardian",
        "name_cn": "森罗万象",
        "cost": 45,
        "cooldown": 30000,
        "healing": 50,
        "duration": 10000,
        "icon": "wood_wood_icon",
        "effect": "assets/skills/effects/tree_guardian.gif",
        "description": "Summons healing tree spirit"
    },
    "fire_fire": {
        "name": "Fire Dragon",
        "name_cn": "火龙术",
        "cost": 50,
        "cooldown": 60000,
        "damage": 999,  # 秒杀
        "icon": "fire_fire_icon",
        "effect": "assets/skills/effects/fire_dragon.gif",
        "description": "Full screen ultimate fire attack"
    },
    "water_water": {
        "name": "Absolute Zero",
        "name_cn": "绝对零度",
        "cost": 55,
        "cooldown": 45000,
        "damage": 80,
        "icon": "water_water_icon",
        "effect": "assets/skills/effects/tsunami.gif",
        "description": "Massive water wave"
    },
    "earth_earth": {
        "name": "Earthquake",
        "name_cn": "地震术",
        "cost": 60,
        "cooldown": 50000,
        "damage": 70,
        "icon": "earth_earth_icon",
        "effect": "assets/skills/effects/earthquake.gif",
        "description": "Ground shaking attack"
    },
    "wood_water": {
        "name": "Life Embrace",
        "name_cn": "生命缠绕",
        "cost": 35,
        "cooldown": 20000,
        "healing": 40,
        "icon": "wood_water_icon",
        "effect": "assets/skills/effects/life_embrace.gif",
        "description": "Healing flower in center"
    },
    "wood_fire": {
        "name": "Burning Flame",
        "name_cn": "焚天烈焰",
        "cost": 40,
        "cooldown": 25000,
        "damage": 35,
        "icon": "wood_fire_icon",
        "effect": "assets/skills/effects/burning_flame.gif",
        "description": "Fire tornado attack"
    },
    "water_fire": {
        "name": "Steam Explosion",
        "name_cn": "蒸汽爆炸",
        "cost": 45,
        "cooldown": 30000,
        "damage": 40,
        "icon": "water_fire_icon",
        "effect": "assets/skills/effects/steam_explosion.gif",
        "description": "Area explosion damage"
    },
    "water_earth": {
        "name": "Swamp Trap",
        "name_cn": "沼泽陷阱",
        "cost": 38,
        "cooldown": 22000,
        "damage": 15,
        "slow_factor": 0.3,
        "icon": "water_earth_icon",
        "effect": "assets/skills/effects/swamp_trap.gif",
        "description": "Slowing mud trap"
    }
}

LEVEL_CONFIG = {
    "initial_enemies": 4,
    "enemy_increment": 1,
    "hp_increment": 10,  # 每关敌人血量增加值
    "portal_image": "assets/objects/portal.png"
}

