#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查找敌人朝向更新逻辑
"""

def find_facing_direction():
    """查找敌人朝向更新逻辑"""
    try:
        with open('enemies.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print("搜索敌人朝向相关代码...")
        
        keywords = ['facing_direction', 'update_facing', 'direction', 'face_direction']
        
        for keyword in keywords:
            print(f"\n搜索关键词: '{keyword}'")
            found_lines = []
            
            for i, line in enumerate(lines, 1):
                if keyword in line:
                    found_lines.append((i, line.strip()))
            
            if found_lines:
                print(f"  找到 {len(found_lines)} 处:")
                for line_num, line_content in found_lines:
                    print(f"    第{line_num}行: {line_content}")
            else:
                print(f"  未找到")
        
        # 查找update_facing_direction方法
        print(f"\n查找update_facing_direction方法:")
        for i, line in enumerate(lines, 1):
            if 'def update_facing_direction' in line:
                print(f"找到方法在第{i}行")
                
                # 显示方法内容
                start = max(0, i-1)
                end = min(len(lines), i+20)
                print(f"\n方法内容 (第{start+1}-{end}行):")
                for j in range(start, end):
                    marker = ">>> " if j == i-1 else "    "
                    print(f"{marker}{j+1:4d}: {lines[j].rstrip()}")
                print()
        
    except Exception as e:
        print(f"搜索失败: {e}")

if __name__ == "__main__":
    find_facing_direction()
