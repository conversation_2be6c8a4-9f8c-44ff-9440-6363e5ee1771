#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查找攻击状态处理
"""

def find_attack_state():
    """查找攻击状态处理"""
    try:
        with open('enemies.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print("搜索攻击状态处理...")
        
        # 查找攻击相关方法
        attack_methods = []
        for i, line in enumerate(lines, 1):
            if ('def ' in line and 'attack' in line.lower()) or 'state == "attack"' in line:
                attack_methods.append((i, line.strip()))
        
        if attack_methods:
            print(f"找到攻击相关代码:")
            for line_num, line_content in attack_methods:
                print(f"  第{line_num}行: {line_content}")
        
        # 查找状态处理逻辑
        print(f"\n查找状态处理逻辑:")
        for i, line in enumerate(lines, 1):
            if 'if self.state ==' in line:
                print(f"第{i}行: {line.strip()}")
                
                # 显示周围代码
                start = max(0, i-2)
                end = min(len(lines), i+8)
                print(f"  周围代码 (第{start+1}-{end}行):")
                for j in range(start, end):
                    marker = ">>> " if j == i-1 else "    "
                    print(f"  {marker}{j+1:4d}: {lines[j].rstrip()}")
                print()
        
    except Exception as e:
        print(f"搜索失败: {e}")

if __name__ == "__main__":
    find_attack_state()
