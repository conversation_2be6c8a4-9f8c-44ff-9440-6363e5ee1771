#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查找水波术技能范围控制代码
"""

def find_water_wave():
    """查找水波术技能范围控制代码"""
    files_to_search = ['combat_system.py', 'config.py', 'main.py']
    
    for filename in files_to_search:
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"\n搜索文件: {filename}")
            print("-" * 40)
            
            keywords = ['水波术', 'water_wave', 'water', 'wave', '水系', 'WaterWave']
            
            for keyword in keywords:
                found_lines = []
                for i, line in enumerate(lines, 1):
                    if keyword in line:
                        found_lines.append((i, line.strip()))
                
                if found_lines:
                    print(f"\n关键词 '{keyword}':")
                    for line_num, line_content in found_lines:
                        print(f"  第{line_num}行: {line_content}")
                        
                        # 显示周围代码
                        if 'def ' in line_content or 'class ' in line_content:
                            start = max(0, i-1)
                            end = min(len(lines), i+15)
                            print(f"    周围代码 (第{start+1}-{end}行):")
                            for j in range(start, end):
                                marker = ">>> " if j == i-1 else "    "
                                print(f"    {marker}{j+1:4d}: {lines[j].rstrip()}")
                            print()
        
        except FileNotFoundError:
            print(f"文件 {filename} 不存在")
        except Exception as e:
            print(f"搜索 {filename} 失败: {e}")

if __name__ == "__main__":
    find_water_wave()
