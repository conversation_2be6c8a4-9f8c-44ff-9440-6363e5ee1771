#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查找组合技能配置
"""

def find_combo_config():
    """查找组合技能配置"""
    try:
        with open('config.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print("搜索组合技能配置...")
        
        for i, line in enumerate(lines, 1):
            if 'COMBO_SKILLS' in line:
                print(f"找到COMBO_SKILLS配置在第{i}行")
                
                # 显示配置内容
                start = max(0, i-1)
                end = min(len(lines), i+100)
                print(f"\nCOMBO_SKILLS配置 (第{start+1}-{end}行):")
                for j in range(start, end):
                    marker = ">>> " if j == i-1 else "    "
                    print(f"{marker}{j+1:4d}: {lines[j].rstrip()}")
                    
                    # 如果遇到下一个配置项就停止
                    if j > i and lines[j].strip() and not lines[j].startswith(' ') and not lines[j].startswith('\t') and '=' in lines[j] and 'COMBO_SKILLS' not in lines[j]:
                        break
                print()
                break
        
    except Exception as e:
        print(f"搜索失败: {e}")

if __name__ == "__main__":
    find_combo_config()
