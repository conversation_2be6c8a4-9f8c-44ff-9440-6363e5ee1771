#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查找技能与敌人碰撞检测相关代码
"""

def find_skill_collision():
    """查找技能与敌人碰撞检测相关代码"""
    try:
        with open('combat_system.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print("搜索技能碰撞检测相关代码...")
        
        keywords = ['collision', 'collide', 'hit', 'damage', 'enemy', 'check_collision']
        
        for keyword in keywords:
            print(f"\n搜索关键词: {keyword}")
            found_lines = []
            
            for i, line in enumerate(lines, 1):
                if keyword in line.lower() and ('def ' in line or 'enemy' in line.lower() or 'damage' in line.lower()):
                    found_lines.append((i, line.strip()))
            
            if found_lines:
                print(f"找到相关代码:")
                for line_num, line_content in found_lines:
                    print(f"  第{line_num}行: {line_content}")
                    
                    # 如果是函数定义，显示更多上下文
                    if 'def ' in line_content and 'collision' in line_content.lower():
                        start = max(0, i-2)
                        end = min(len(lines), i+30)
                        print(f"    函数内容 (第{start+1}-{end}行):")
                        for j in range(start, end):
                            marker = ">>> " if j == i-1 else "    "
                            print(f"    {marker}{j+1:4d}: {lines[j].rstrip()}")
                        print()
        
    except Exception as e:
        print(f"搜索失败: {e}")

if __name__ == "__main__":
    find_skill_collision()
