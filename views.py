# views.py
import pygame
from config import SCREEN_WIDTH, SCREEN_HEIGHT, IMAGE_PATHS, BASE_SKILLS, COMBO_SKILLS
from level import LevelSystem


class GameView:
    def __init__(self):
        self.images = {}
        self.background = None  # 新增背景缓存
        self.load_images()

    def load_images(self):
        for name, path in IMAGE_PATHS.items():
            try:
                if name == "background":
                    bg = pygame.image.load(path).convert()
                    self.background = pygame.transform.scale(bg, (SCREEN_WIDTH, SCREEN_HEIGHT))
                else:
                    self.images[name] = pygame.image.load(path).convert_alpha()
            except Exception as e:
                print(f"Error loading {path}: {e}")
                self.images[name] = pygame.Surface((32, 32))

    def draw(self, screen, player, get_enemy_func, input_handler, level_system, combat_system):
        # 绘制背景
        screen.fill((0, 0, 0))
        screen.blit(self.background, (0, 0))

        # 创建绘制层次
        # 1. 地面层：传送门
        if level_system.portal:
            screen.blit(level_system.portal.image, level_system.portal.rect)

        # 2. 效果层：地面效果
        for effect in level_system.effects:
            if hasattr(effect, 'image') and hasattr(effect, 'rect'):
                # 只绘制地面效果，角色效果稍后绘制
                if not hasattr(effect, 'is_character_effect') or not effect.is_character_effect:
                    screen.blit(effect.image, effect.rect)

        # 3. 角色层：玩家和敌人
        # 绘制玩家
        screen.blit(player.image, player.rect)

        # 绘制敌人
        for enemy in level_system.enemies:
            if enemy.hp > 0:
                # 使用image_rect绘制图像，使用rect进行碰撞检测
                if hasattr(enemy, 'image_rect'):
                    screen.blit(enemy.image, enemy.image_rect)
                    info_x, info_y = enemy.image_rect.x, enemy.image_rect.y
                else:
                    # 向后兼容，如果没有image_rect就使用rect
                    screen.blit(enemy.image, enemy.rect)
                    info_x, info_y = enemy.rect.x, enemy.rect.y

                # 绘制敌人信息
                font = pygame.font.Font(None, 24)
                type_text = font.render(f"Type: {enemy.enemy_type}", True, (255, 255, 0))
                hp_text = font.render(f"HP: {int(enemy.hp)}", True, (255, 0, 0))
                screen.blit(type_text, (info_x, info_y - 40))
                screen.blit(hp_text, (info_x, info_y - 20))

                # 调试：绘制碰撞矩形边框（可选）
                # pygame.draw.rect(screen, (255, 0, 0), enemy.rect, 1)  # 红色碰撞边框
                # pygame.draw.rect(screen, (0, 255, 0), enemy.image_rect, 1)  # 绿色图像边框

        # 4. 投射物层
        for projectile in level_system.projectiles:
            if hasattr(projectile, 'image') and hasattr(projectile, 'rect'):
                screen.blit(projectile.image, projectile.rect)

        # 5. 角色效果层：附加在角色上的效果
        for effect in level_system.effects:
            if hasattr(effect, 'image') and hasattr(effect, 'rect'):
                if hasattr(effect, 'is_character_effect') and effect.is_character_effect:
                    screen.blit(effect.image, effect.rect)

        # 6. 技能视觉效果和粒子效果
        combat_system.draw_effects(screen)

        # 7. UI层
        self.draw_ui(screen, player, input_handler, level_system, combat_system)

    def draw_ui(self, screen, player, input_handler, level_system, combat_system):
        # 绘制输入框
        if input_handler.casting:
            input_handler.draw(screen)
            font = pygame.font.Font(None, 32)
            text_surf = font.render(input_handler.input_text, True, (255, 255, 255))
            screen.blit(text_surf, (input_handler.input_rect.x + 5, input_handler.input_rect.y + 5))

        # 绘制状态栏
        pygame.draw.rect(screen, (50, 50, 50), (10, 10, 104, 54))  # 背景
        pygame.draw.rect(screen, (255, 0, 0), (12, 12, player.hp, 20))  # 血量
        pygame.draw.rect(screen, (0, 0, 255), (12, 42, player.mp, 20))  # 法力值

        # 绘制状态文本
        font = pygame.font.Font(None, 20)
        hp_text = font.render(f"HP: {player.hp}/100", True, (255, 255, 255))
        mp_text = font.render(f"MP: {player.mp}/100", True, (255, 255, 255))
        screen.blit(hp_text, (12, 32))
        screen.blit(mp_text, (12, 62))

        # 绘制关卡信息
        level_text = font.render(f"关卡: {level_system.current_level}", True, (255, 255, 255))
        screen.blit(level_text, (SCREEN_WIDTH - 100, 10))

        # 绘制技能图标 - 在屏幕左侧以列的方式排列
        self.draw_skill_icons(screen, player, combat_system)

    def draw_skill_select(self, screen, combat_system):
        """绘制技能选择界面"""
        # 添加半透明背景
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 180))  # 黑色半透明
        screen.blit(overlay, (0, 0))

        # 绘制标题
        font_title = pygame.font.Font(None, 48)
        title_text = font_title.render("选择一个新技能", True, (255, 255, 255))
        screen.blit(title_text, (SCREEN_WIDTH // 2 - title_text.get_width() // 2, 100))

        # 绘制技能选项
        font_skill = pygame.font.Font(None, 36)

        if not combat_system.skill_selection:
            # 如果没有技能可选，显示提示信息
            no_skill_text = font_skill.render("没有可用的新技能", True, (255, 200, 200))
            screen.blit(no_skill_text, (SCREEN_WIDTH // 2 - no_skill_text.get_width() // 2, 300))
            return

        for i, skill_name in enumerate(combat_system.skill_selection):
            # 绘制技能选择框
            rect = pygame.Rect(SCREEN_WIDTH // 2 - 150, 200 + i * 120, 300, 100)
            pygame.draw.rect(screen, (50, 50, 80), rect)
            pygame.draw.rect(screen, (100, 100, 200), rect, 3)

            # 获取技能信息
            if skill_name in COMBO_SKILLS:
                skill_info = COMBO_SKILLS[skill_name]
                skill_display_name = skill_info["name"]

                # 绘制技能名称
                name_text = font_skill.render(skill_display_name, True, (255, 255, 255))
                screen.blit(name_text, (rect.centerx - name_text.get_width() // 2, rect.y + 20))

                # 绘制技能描述
                font_desc = pygame.font.Font(None, 24)
                if "damage" in skill_info:
                    desc_text = font_desc.render(f"伤害: {skill_info['damage']} | 消耗: {skill_info['cost']} MP", True, (200, 200, 255))
                elif "shield" in skill_info:
                    desc_text = font_desc.render(f"护盾: {skill_info['shield']} | 消耗: {skill_info['cost']} MP", True, (200, 200, 255))
                else:
                    desc_text = font_desc.render(f"消耗: {skill_info['cost']} MP", True, (200, 200, 255))

                screen.blit(desc_text, (rect.centerx - desc_text.get_width() // 2, rect.y + 60))

        # 添加提示信息
        hint_font = pygame.font.Font(None, 24)
        hint_text = hint_font.render("点击选择一个技能", True, (200, 200, 200))
        screen.blit(hint_text, (SCREEN_WIDTH // 2 - hint_text.get_width() // 2, SCREEN_HEIGHT - 100))

    def draw_skill_icons(self, screen, player, combat_system):
        """在屏幕左侧绘制技能图标"""
        current_time = pygame.time.get_ticks()
        icon_size = 50
        icon_spacing = 10
        start_x = 10
        start_y = 100

        # 创建字体对象
        font = pygame.font.Font(None, 20)

        # 绘制基础技能图标
        for i, skill_name in enumerate(player.unlocked_skills):
            # 检查技能名称是否在BASE_SKILLS中
            if skill_name not in BASE_SKILLS:
                print(f"警告: 技能 {skill_name} 不在BASE_SKILLS中")
                continue

            # 获取技能信息
            skill_info = BASE_SKILLS[skill_name]

            # 计算位置
            x = start_x
            y = start_y + i * (icon_size + icon_spacing)

            # 绘制图标背景
            icon_bg_rect = pygame.Rect(x, y, icon_size, icon_size)
            pygame.draw.rect(screen, (50, 50, 50), icon_bg_rect)
            pygame.draw.rect(screen, (200, 200, 200), icon_bg_rect, 1)

            # 绘制技能图标
            if "icon" in skill_info:
                icon_key = skill_info["icon"]
                if icon_key in self.images:
                    icon = self.images[icon_key]
                    icon = pygame.transform.scale(icon, (icon_size - 4, icon_size - 4))
                    screen.blit(icon, (x + 2, y + 2))

            # 检查冷却状态
            if skill_name in player.cooldowns:
                cooldown_time = skill_info["cooldown"]
                elapsed = current_time - player.cooldowns[skill_name]

                if elapsed < cooldown_time:
                    # 绘制冷却遮罩
                    cooldown_height = int((1 - elapsed / cooldown_time) * icon_size)
                    cooldown_rect = pygame.Rect(x, y, icon_size, cooldown_height)
                    cooldown_surface = pygame.Surface((icon_size, cooldown_height), pygame.SRCALPHA)
                    cooldown_surface.fill((0, 0, 0, 150))
                    screen.blit(cooldown_surface, cooldown_rect)

                    # 显示剩余冷却时间
                    seconds_left = (cooldown_time - elapsed) / 1000
                    time_text = font.render(f"{seconds_left:.1f}", True, (255, 255, 255))
                    screen.blit(time_text, (x + 5, y + icon_size // 2 - 10))

            # 绘制快捷键
            key_text = font.render(str(i + 1), True, (255, 255, 255))
            screen.blit(key_text, (x + icon_size - 15, y + icon_size - 15))

        # 绘制组合技能图标
        for i, skill_name in enumerate(player.combo_skills):
            # 检查技能名称是否在COMBO_SKILLS中
            if skill_name not in COMBO_SKILLS:
                print(f"警告: 技能 {skill_name} 不在COMBO_SKILLS中")
                continue

            # 获取技能信息
            skill_info = COMBO_SKILLS[skill_name]

            # 计算位置
            x = start_x + icon_size + icon_spacing
            y = start_y + i * (icon_size + icon_spacing)

            # 绘制图标背景
            icon_bg_rect = pygame.Rect(x, y, icon_size, icon_size)
            pygame.draw.rect(screen, (50, 50, 50), icon_bg_rect)
            pygame.draw.rect(screen, (200, 200, 200), icon_bg_rect, 1)

            # 绘制技能图标
            if "icon" in skill_info:
                icon_key = skill_info["icon"]
                try:
                    # 优先从缓存中获取
                    if icon_key in self.images:
                        icon = self.images[icon_key]
                        icon = pygame.transform.scale(icon, (icon_size - 4, icon_size - 4))
                        screen.blit(icon, (x + 2, y + 2))
                    # 如果缓存中没有，尝试从 IMAGE_PATHS 加载
                    elif icon_key in IMAGE_PATHS:
                        icon = pygame.image.load(IMAGE_PATHS[icon_key]).convert_alpha()
                        icon = pygame.transform.scale(icon, (icon_size - 4, icon_size - 4))
                        screen.blit(icon, (x + 2, y + 2))
                        # 添加到缓存
                        self.images[icon_key] = pygame.image.load(IMAGE_PATHS[icon_key]).convert_alpha()
                    else:
                        # 使用默认图标
                        pygame.draw.rect(screen, (100, 100, 100), (x + 2, y + 2, icon_size - 4, icon_size - 4))
                        font = pygame.font.Font(None, 24)
                        text = font.render("?", True, (255, 255, 255))
                        text_rect = text.get_rect(center=(x + icon_size//2, y + icon_size//2))
                        screen.blit(text, text_rect)
                        print(f"组合技能图标未找到: {icon_key}")
                except Exception as e:
                    print(f"加载组合技能图标失败 {icon_key}: {e}")
                    # 绘制默认图标
                    pygame.draw.rect(screen, (100, 100, 100), (x + 2, y + 2, icon_size - 4, icon_size - 4))
                    font = pygame.font.Font(None, 24)
                    text = font.render("?", True, (255, 255, 255))
                    text_rect = text.get_rect(center=(x + icon_size//2, y + icon_size//2))
                    screen.blit(text, text_rect)

            # 检查冷却状态
            if skill_name in player.cooldowns:
                cooldown_time = skill_info["cooldown"]
                elapsed = current_time - player.cooldowns[skill_name]

                if elapsed < cooldown_time:
                    # 绘制冷却遮罩
                    cooldown_height = int((1 - elapsed / cooldown_time) * icon_size)
                    cooldown_rect = pygame.Rect(x, y, icon_size, cooldown_height)
                    cooldown_surface = pygame.Surface((icon_size, cooldown_height), pygame.SRCALPHA)
                    cooldown_surface.fill((0, 0, 0, 150))
                    screen.blit(cooldown_surface, cooldown_rect)

                    # 显示剩余冷却时间
                    seconds_left = (cooldown_time - elapsed) / 1000
                    time_text = font.render(f"{seconds_left:.1f}", True, (255, 255, 255))
                    screen.blit(time_text, (x + 5, y + icon_size // 2 - 10))

        # 绘制组合技能提示
        self.draw_combo_hint(screen, player, combat_system, font)

        # 绘制组合技能按键提示
        self.draw_combo_key_hints(screen, player, start_x, start_y, icon_size, icon_spacing)

    def get_combo_key_hint(self, combo_name):
        """获取组合技能的按键提示文本"""
        # 技能名称到按键的映射
        skill_to_key = {
            "metal": "1",
            "wood": "2",
            "water": "3",
            "fire": "4",
            "earth": "5"
        }

        # 解析组合技能名称
        if "_" in combo_name:
            parts = combo_name.split("_")
            if len(parts) == 2:
                skill1, skill2 = parts
                key1 = skill_to_key.get(skill1, "?")
                key2 = skill_to_key.get(skill2, "?")

                if skill1 == skill2:
                    # 相同技能组合（双击）
                    return f"{key1}+{key2}"
                else:
                    # 不同技能组合
                    return f"{key1}+{key2}"

        return "?"

    def draw_combo_key_hints(self, screen, player, start_x, start_y, icon_size, icon_spacing):
        """绘制组合技能的按键提示"""
        # 创建小号字体
        hint_font = pygame.font.Font(None, 16)
        hint_color = (180, 180, 180)  # 深灰色

        # 绘制组合技能的按键提示
        for i, skill_name in enumerate(player.combo_skills):
            # 只为已解锁的组合技能显示提示
            if skill_name in COMBO_SKILLS:
                # 计算组合技能图标位置
                x = start_x + icon_size + icon_spacing
                y = start_y + i * (icon_size + icon_spacing)

                # 获取按键提示文本
                key_hint = self.get_combo_key_hint(skill_name)

                # 绘制提示文本（在图标下方）
                hint_text = hint_font.render(key_hint, True, hint_color)
                hint_x = x + (icon_size - hint_text.get_width()) // 2  # 居中对齐
                hint_y = y + icon_size + 2  # 图标下方2像素

                # 绘制半透明背景（可选）
                bg_rect = pygame.Rect(hint_x - 2, hint_y - 1, hint_text.get_width() + 4, hint_text.get_height() + 2)
                bg_surface = pygame.Surface((bg_rect.width, bg_rect.height), pygame.SRCALPHA)
                bg_surface.fill((0, 0, 0, 100))  # 黑色半透明背景
                screen.blit(bg_surface, bg_rect)

                # 绘制文本
                screen.blit(hint_text, (hint_x, hint_y))

    def draw_combo_hint(self, screen, player, combat_system, font):
        """绘制组合技能提示"""
        current_time = pygame.time.get_ticks()

        # 检查是否有第一个技能按键
        if (hasattr(player, 'last_skill_key') and player.last_skill_key and
            hasattr(player, 'last_skill_time') and player.last_skill_time and
            hasattr(player, 'combo_window') and player.combo_window):

            # 检查是否在组合窗口内
            time_since_last = current_time - player.last_skill_time
            if time_since_last <= player.combo_window:
                # 绘制组合提示
                hint_y = 50
                hint_font = pygame.font.Font(None, 32)

                # 绘制背景
                hint_bg = pygame.Rect(SCREEN_WIDTH // 2 - 200, hint_y - 10, 400, 60)
                pygame.draw.rect(screen, (0, 0, 0, 180), hint_bg)
                pygame.draw.rect(screen, (255, 215, 0), hint_bg, 2)

                # 绘制提示文字
                hint_text = hint_font.render(f"Combo Ready! Last: {player.last_skill_key.upper()}", True, (255, 215, 0))
                hint_rect = hint_text.get_rect(center=(SCREEN_WIDTH // 2, hint_y + 10))
                screen.blit(hint_text, hint_rect)

                # 绘制时间条
                progress = 1 - (time_since_last / player.combo_window)
                progress_width = int(380 * progress)
                progress_rect = pygame.Rect(SCREEN_WIDTH // 2 - 190, hint_y + 30, progress_width, 8)
                pygame.draw.rect(screen, (255, 215, 0), progress_rect)

                # 绘制可用组合技能
                available_combos = []
                for skill in ["metal", "wood", "water", "fire", "earth"]:
                    combo_name = combat_system.check_combo(player.last_skill_key, skill)
                    if combo_name and combo_name in player.combo_skills:
                        available_combos.append((skill, combo_name))

                if available_combos:
                    combo_hint_y = hint_y + 50
                    combo_font = pygame.font.Font(None, 24)
                    combo_text = "Available: " + ", ".join([f"{skill.upper()}({COMBO_SKILLS[combo]['name']})" for skill, combo in available_combos])
                    combo_surface = combo_font.render(combo_text, True, (200, 255, 200))
                    combo_rect = combo_surface.get_rect(center=(SCREEN_WIDTH // 2, combo_hint_y))
                    screen.blit(combo_surface, combo_rect)