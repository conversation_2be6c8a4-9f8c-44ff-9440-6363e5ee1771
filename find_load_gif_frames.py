#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查找load_gif_frames函数实现
"""

def find_load_gif_frames():
    """查找load_gif_frames函数实现"""
    try:
        with open('combat_system.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print("搜索load_gif_frames函数...")
        
        for i, line in enumerate(lines, 1):
            if 'def load_gif_frames' in line:
                print(f"找到load_gif_frames函数在第{i}行")
                
                # 显示函数完整实现
                start = max(0, i-1)
                end = min(len(lines), i+50)
                print(f"\nload_gif_frames函数实现 (第{start+1}-{end}行):")
                for j in range(start, end):
                    marker = ">>> " if j == i-1 else "    "
                    print(f"{marker}{j+1:4d}: {lines[j].rstrip()}")
                    
                    # 如果遇到下一个函数定义就停止
                    if j > i and lines[j].strip().startswith('def ') and 'load_gif_frames' not in lines[j]:
                        break
                print()
                break
        
    except Exception as e:
        print(f"搜索失败: {e}")

if __name__ == "__main__":
    find_load_gif_frames()
