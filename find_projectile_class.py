#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查找Projectile类的完整定义
"""

def find_projectile_class():
    """查找Projectile类的完整定义"""
    try:
        with open('enemies.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print("搜索Projectile类...")
        
        in_projectile_class = False
        projectile_lines = []
        
        for i, line in enumerate(lines, 1):
            if 'class Projectile' in line:
                in_projectile_class = True
                print(f"找到Projectile类在第{i}行")
            elif in_projectile_class and line.strip().startswith('class ') and 'Projectile' not in line:
                break
            elif in_projectile_class:
                projectile_lines.append((i, line.rstrip()))
        
        if projectile_lines:
            print(f"\nProjectile类完整内容:")
            for line_num, line_content in projectile_lines:
                print(f"{line_num:4d}: {line_content}")
        else:
            print("未找到Projectile类")
        
    except Exception as e:
        print(f"搜索失败: {e}")

if __name__ == "__main__":
    find_projectile_class()
