#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查找技能组合检测系统相关代码
"""

def find_combo_detection():
    """查找技能组合检测系统相关代码"""
    try:
        with open('combat_system.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print("搜索技能组合检测相关代码...")
        
        keywords = ['combo', 'detect_combo', 'combo_buffer', 'combination', '组合', '焚天烈焰', '绝对零度', '火龙术']
        
        for keyword in keywords:
            print(f"\n搜索关键词: {keyword}")
            found_lines = []
            
            for i, line in enumerate(lines, 1):
                if keyword in line:
                    found_lines.append((i, line.strip()))
            
            if found_lines:
                print(f"找到相关代码:")
                for line_num, line_content in found_lines:
                    print(f"  第{line_num}行: {line_content}")
                    
                    # 如果是函数定义，显示更多上下文
                    if 'def ' in line_content and ('combo' in line_content.lower() or 'detect' in line_content.lower()):
                        start = max(0, i-2)
                        end = min(len(lines), i+40)
                        print(f"    函数内容 (第{start+1}-{end}行):")
                        for j in range(start, end):
                            marker = ">>> " if j == i-1 else "    "
                            print(f"    {marker}{j+1:4d}: {lines[j].rstrip()}")
                            
                            # 如果遇到下一个函数定义就停止
                            if j > i and lines[j].strip().startswith('def ') and keyword not in lines[j]:
                                break
                        print()
        
    except Exception as e:
        print(f"搜索失败: {e}")

if __name__ == "__main__":
    find_combo_detection()
