# main.py
import pygame
from config import SCREEN_WIDTH, SCREEN_HEIGHT, FPS
from player import Player
from enemies import Enemy
from input_handler import InputHandler
from combat_system import CombatSystem
from views import GameView
from level import LevelSystem
from start_screen import StartScreen
from save_system import SaveSystem
from death_screen import DeathScreen

class Game:
    def __init__(self):
        """初始化游戏"""
        pygame.init()
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("元素魔法师")
        self.clock = pygame.time.Clock()
        self.running = True
        self.debug_mode = False  # 关闭调试模式，提高性能
        self.game_state = "START"  # 状态: START, LOADING, PLAYING, SKILL_SELECT, DEATH

        # 初始化界面
        self.start_screen = StartScreen()
        self.death_screen = DeathScreen()
        self.save_system = SaveSystem()

        # 初始化视图系统（无论游戏状态如何都需要）
        self.view = GameView()

        # 游戏组件在进入PLAYING状态时初始化
        self.background = None
        self.level_system = None
        self.player = None
        self.input_handler = None
        self.combat_system = None

    def initialize_game(self):
        # 加载游戏资源
        self.background = pygame.image.load("assets/background/ground.png").convert()
        self.background = pygame.transform.scale(self.background, (SCREEN_WIDTH, SCREEN_HEIGHT))
        self.player = Player()

        # 设置玩家的游戏引用
        self.player.game = self

        # 初始化战斗系统
        self.combat_system = CombatSystem(self.player)

        # 初始化输入处理器 - 修复参数传递
        self.input_handler = InputHandler(self.player, self.combat_system)

        # 初始化关卡系统
        self.level_system = LevelSystem(self)

        # 初始化关卡
        self.level_system.spawn_enemies()
        self.level_system.walls = self.level_system.generate_walls(self.background)

        # 初始化玩家位置 - 修改为屏幕中央
        self.player.rect.center = (SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)

        # 确保玩家不在墙体内
        self._ensure_player_not_in_wall()

    def generate_level(self, level_number):
        """生成指定关卡"""
        self.level_system.current_level = level_number
        self.level_system.spawn_enemies()
        self.level_system.walls = self.level_system.generate_walls(self.background)

        # 初始化玩家位置 - 修改为屏幕中央
        self.player.rect.center = (SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)

        # 确保玩家不在墙体内
        self._ensure_player_not_in_wall()

    def _ensure_player_not_in_wall(self):
        """确保玩家不在墙体内部"""
        # 检查玩家是否在墙体内
        in_wall = False
        for wall in self.level_system.walls:
            if self.player.rect.colliderect(wall):
                in_wall = True
                break

        # 如果在墙体内，尝试找到一个安全位置
        if in_wall:
            print("玩家初始位置在墙体内，尝试找到安全位置")
            safe_positions = [
                (SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2),
                (100, 100),
                (SCREEN_WIDTH - 100, 100),
                (100, SCREEN_HEIGHT - 100),
                (SCREEN_WIDTH - 100, SCREEN_HEIGHT - 100)
            ]

            for pos in safe_positions:
                self.player.rect.center = pos
                in_wall = False
                for wall in self.level_system.walls:
                    if self.player.rect.colliderect(wall):
                        in_wall = True
                        break
                if not in_wall:
                    print(f"找到安全位置: {pos}")
                    break

    def restart_game(self):
        """重新开始游戏"""
        print("🔄 重置游戏状态...")

        # 重置关卡
        self.current_level = 1

        # 重新初始化游戏
        self.initialize_game()

        # 返回游戏状态
        self.game_state = "PLAYING"

        print("✅ 游戏重新开始完成")

    def next_level(self):
        """进入下一关"""
        print(f"玩家进入传送门，准备进入下一关")

        # 增加关卡等级
        self.level_system.current_level += 1

        # 清除传送门
        self.level_system.portal = None

        # 重置玩家位置
        self.player.reset_position()

        # 生成新关卡的敌人
        self.level_system.spawn_enemies()

        # 提供技能选择
        self.combat_system.offer_new_skills()

        print(f"进入关卡 {self.level_system.current_level}")

    def proceed_to_next_level(self):
        """从技能选择界面进入下一关"""
        print(f"技能选择完成，进入关卡 {self.level_system.current_level}")

        # 重新生成关卡（如果需要）
        if len(self.level_system.enemies) == 0:
            self.level_system.spawn_enemies()

        # 确保玩家不在墙体内
        self._ensure_player_not_in_wall()

    def draw_skill_selection(self):
        """绘制技能选择界面"""
        # 绘制背景
        self.screen.fill((30, 30, 50))

        # 使用系统字体或默认字体
        try:
            # 尝试使用系统字体
            font_large = pygame.font.SysFont('Jokerman', 48, bold=True)
            font_medium = pygame.font.SysFont('Algerian', 32, bold=True)
            font_small = pygame.font.SysFont('Algerian', 24)
        except:
            # 如果系统字体不可用，使用默认字体
            font_large = pygame.font.Font(None, 48)
            font_medium = pygame.font.Font(None, 32)
            font_small = pygame.font.Font(None, 24)

        # 绘制标题
        title_text = font_large.render("Choose New Skill", True, (255, 255, 255))
        title_rect = title_text.get_rect(center=(SCREEN_WIDTH // 2, 80))
        self.screen.blit(title_text, title_rect)

        # 绘制技能选项
        if hasattr(self.combat_system, 'skill_selection') and self.combat_system.skill_selection:
            from config import COMBO_SKILLS, IMAGE_PATHS

            y_start = 150
            icon_size = 64

            for i, skill_name in enumerate(self.combat_system.skill_selection):
                if skill_name in COMBO_SKILLS:
                    skill_info = COMBO_SKILLS[skill_name]

                    # 绘制技能框
                    skill_rect = pygame.Rect(SCREEN_WIDTH // 2 - 350, y_start + i * 200, 700, 110)
                    pygame.draw.rect(self.screen, (60, 60, 80), skill_rect)
                    pygame.draw.rect(self.screen, (100, 150, 200), skill_rect, 3)

                    # 绘制数字标签
                    number_rect = pygame.Rect(skill_rect.x + 10, skill_rect.y + 10, 30, 30)
                    pygame.draw.circle(self.screen, (255, 255, 255), number_rect.center, 15)
                    number_text = font_medium.render(str(i+1), True, (0, 0, 0))
                    number_pos = (number_rect.centerx - number_text.get_width()//2,
                                 number_rect.centery - number_text.get_height()//2)
                    self.screen.blit(number_text, number_pos)

                    # 绘制技能图标
                    icon_rect = pygame.Rect(skill_rect.x + 60, skill_rect.y + 20, icon_size, icon_size)
                    pygame.draw.rect(self.screen, (40, 40, 60), icon_rect)
                    pygame.draw.rect(self.screen, (150, 150, 150), icon_rect, 2)

                    # 尝试加载技能图标
                    icon_key = skill_info.get('icon')
                    if icon_key and icon_key in IMAGE_PATHS:
                        try:
                            icon_image = pygame.image.load(IMAGE_PATHS[icon_key]).convert_alpha()
                            icon_image = pygame.transform.scale(icon_image, (icon_size-4, icon_size-4))
                            self.screen.blit(icon_image, (icon_rect.x + 2, icon_rect.y + 2))
                        except Exception as e:
                            # 如果加载失败，绘制默认图标
                            pygame.draw.rect(self.screen, (100, 100, 100),
                                           (icon_rect.x + 2, icon_rect.y + 2, icon_size-4, icon_size-4))
                            # 绘制问号
                            question_text = font_medium.render("?", True, (255, 255, 255))
                            question_pos = (icon_rect.centerx - question_text.get_width()//2,
                                          icon_rect.centery - question_text.get_height()//2)
                            self.screen.blit(question_text, question_pos)
                    else:
                        # 没有图标时的默认显示
                        pygame.draw.rect(self.screen, (100, 100, 100),
                                       (icon_rect.x + 2, icon_rect.y + 2, icon_size-4, icon_size-4))
                        question_text = font_medium.render("?", True, (255, 255, 255))
                        question_pos = (icon_rect.centerx - question_text.get_width()//2,
                                      icon_rect.centery - question_text.get_height()//2)
                        self.screen.blit(question_text, question_pos)

                    # 绘制技能名称
                    skill_text = font_medium.render(skill_info['name'], True, (255, 255, 255))
                    self.screen.blit(skill_text, (skill_rect.x + 140, skill_rect.y + 15))

                    # 绘制技能描述
                    desc_text = font_small.render(skill_info.get('description', 'No description'), True, (200, 200, 200))
                    self.screen.blit(desc_text, (skill_rect.x + 140, skill_rect.y + 45))

                    # 绘制技能统计
                    stats = []
                    if 'damage' in skill_info:
                        stats.append(f"Damage: {skill_info['damage']}")
                    if 'shield' in skill_info:
                        stats.append(f"Shield: {skill_info['shield']}")
                    if 'healing' in skill_info:
                        stats.append(f"Healing: {skill_info['healing']}")
                    stats.append(f"Cost: {skill_info['cost']} MP")

                    stats_text = font_small.render(" | ".join(stats), True, (150, 200, 255))
                    self.screen.blit(stats_text, (skill_rect.x + 140, skill_rect.y + 70))

        # 绘制提示
        hint_text = font_small.render("Press 1, 2, 3 to select skill", True, (150, 150, 150))
        hint_rect = hint_text.get_rect(center=(SCREEN_WIDTH // 2, SCREEN_HEIGHT - 50))
        self.screen.blit(hint_text, hint_rect)

    def handle_events(self):
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False

            if self.game_state == "START":
                if event.type == pygame.MOUSEBUTTONDOWN:
                    if self.start_screen.check_button(event.pos):
                        self.game_state = "LOADING"
                        self.start_screen.show_loading(self.screen)
                        self.initialize_game()
                        self.game_state = "PLAYING"

            elif self.game_state == "PLAYING":
                if event.type == pygame.KEYDOWN:
                    # 处理技能释放
                    if event.key in [pygame.K_1, pygame.K_2, pygame.K_3, pygame.K_4, pygame.K_5]:
                        self.combat_system.handle_skill_key(event.key)

                    # 传送门交互
                    if event.key == pygame.K_e and self.level_system.portal:
                        if self.player.rect.colliderect(self.level_system.portal.rect):
                            self.level_system.current_level += 1
                            self.player.reset_position()
                            self.level_system.spawn_enemies()
                            self.level_system.portal = None
                            # 关卡结束后提供技能选择
                            self.combat_system.offer_new_skills()

                # 处理文字输入
                spell_text = self.input_handler.handle_input(event)
                if spell_text:
                    target_enemy = self.level_system.first_alive_enemy()
                    if target_enemy:
                        self.combat_system.resolve_spell(spell_text.lower(), target_enemy)

            elif self.game_state == "SKILL_SELECT":
                # 处理技能选择
                if event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_1:
                        self.combat_system.select_skill(0)
                        self.game_state = "PLAYING"
                    elif event.key == pygame.K_2:
                        self.combat_system.select_skill(1)
                        self.game_state = "PLAYING"
                    elif event.key == pygame.K_3:
                        self.combat_system.select_skill(2)
                        self.game_state = "PLAYING"

            elif self.game_state == "DEATH":
                # 处理死亡界面事件
                action = self.death_screen.handle_event(event)
                if action == "RESTART":
                    print("🔄 重新开始游戏")
                    self.restart_game()

    def update(self, dt=16):
        """更新游戏状态"""
        # 检查玩家是否存在
        if self.player is None:
            print("错误: 玩家对象为None，重新创建")
            self.player = Player()
            self.player.game = self
            self.combat_system = CombatSystem(self.player)
            return

        # 更新玩家
        self.player.update(self.level_system.walls, dt)

        # 更新关卡系统
        self.level_system.update(dt)

        # 更新战斗系统
        self.combat_system.update(dt)

        # 检查玩家是否死亡
        if self.player.hp <= 0:
            print(f"💀 玩家死亡！HP: {self.player.hp}")
            self.game_state = "DEATH"
            return

        # 检查玩家与传送门的碰撞
        if self.level_system.portal and self.player.rect.colliderect(self.level_system.portal.rect):
            self.next_level()

        # 打印调试信息
        if pygame.time.get_ticks() % 1000 < 16:  # 每秒打印一次
            print(f"敌人数量: {len(self.level_system.enemies)}")
            print(f"投射物数量: {len(self.level_system.projectiles)}")
            print(f"效果数量: {len(self.level_system.effects)}")

    def draw(self):
        """绘制游戏画面"""
        if self.game_state == "START":
            # 绘制开始界面
            self.start_screen.draw(self.screen)
        elif self.game_state == "SKILL_SELECT":
            # 绘制技能选择界面
            self.draw_skill_selection()
        elif self.game_state == "PLAYING":
            # 确保视图存在
            if self.view is None:
                print("错误: 视图对象为None，重新创建")
                self.view = GameView()

            # 使用视图系统绘制
            self.view.draw(
                self.screen,
                self.player,
                self.level_system.first_alive_enemy,
                self.input_handler,
                self.level_system,
                self.combat_system
            )

            # 调试绘制 - 显示墙体碰撞区域
            if self.debug_mode:
                for wall in self.level_system.walls:
                    pygame.draw.rect(self.screen, (255, 0, 0), wall, 1)

        elif self.game_state == "DEATH":
            # 绘制死亡界面
            self.death_screen.draw(self.screen)

            # 打印墙体数量
            font = pygame.font.Font(None, 24)
            wall_text = font.render(f"墙体数量: {len(self.level_system.walls)}", True, (255, 255, 0))
            self.screen.blit(wall_text, (10, 10))

        # 更新显示
        pygame.display.flip()

    def run(self):
        """游戏主循环"""
        while self.running:
            dt = self.clock.tick(FPS)
            self.handle_events()

            # 根据游戏状态更新
            if self.game_state == "START":
                self.start_screen.update(dt)
            elif self.game_state == "PLAYING":
                self.update(dt)
            elif self.game_state == "SKILL_SELECT":
                # 技能选择状态不需要更新游戏逻辑
                pass
            elif self.game_state == "DEATH":
                # 死亡状态不需要更新游戏逻辑
                pass

            self.draw()

        pygame.quit()

if __name__ == "__main__":
    game = Game()
    game.run()