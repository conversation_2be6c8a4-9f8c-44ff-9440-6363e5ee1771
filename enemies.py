import pygame
import random
import math
from config import ENEMY_STATS, SCREEN_WIDTH, SCREEN_HEIGHT, IMAGE_PATHS

class Enemy(pygame.sprite.Sprite):
    def __init__(self, game, enemy_type="melee", level=1):
        super().__init__()
        self.game = game
        self.enemy_type = enemy_type
        self.level = level

        print(f"初始化 {enemy_type} 敌人，等级: {level}")

        # 加载静态图像，不再使用精灵图
        try:
            if enemy_type == "melee":
                self.image = pygame.image.load(IMAGE_PATHS["melee_enemy"]).convert_alpha()
                print(f"加载近战敌人图像: {IMAGE_PATHS['melee_enemy']}")
            else:  # ranged
                self.image = pygame.image.load(IMAGE_PATHS["ranged_enemy"]).convert_alpha()
                print(f"加载远程敌人图像: {IMAGE_PATHS['ranged_enemy']}")

            # 调整图像大小为64x80像素
            original_size = self.image.get_size()
            self.image = pygame.transform.scale(self.image, (64, 80))
            print(f"敌人图像尺寸: 原始={original_size}, 调整后=64x80")

        except Exception as e:
            print(f"加载敌人图像失败: {e}")
            # 创建默认图像
            self.image = pygame.Surface((64, 80), pygame.SRCALPHA)
            if enemy_type == "melee":
                # 近战敌人 - 红色方块
                pygame.draw.rect(self.image, (255, 0, 0, 200), (0, 0, 64, 80))
                pygame.draw.rect(self.image, (255, 255, 255), (0, 0, 64, 80), 2)
                # 添加武器图案
                pygame.draw.line(self.image, (200, 200, 200), (32, 20), (32, 60), 3)
                pygame.draw.circle(self.image, (200, 200, 200), (32, 20), 8)
            else:
                # 远程敌人 - 蓝色圆形
                pygame.draw.circle(self.image, (0, 0, 255, 200), (32, 40), 30)
                pygame.draw.circle(self.image, (255, 255, 255), (32, 40), 30, 2)
                # 添加弓箭图案
                pygame.draw.arc(self.image, (200, 200, 200), (16, 20, 32, 40), 0, 3.14, 3)
                pygame.draw.line(self.image, (200, 200, 200), (16, 40), (48, 40), 2)

        # 设置碰撞矩形
        self.rect = self.image.get_rect()
        print(f"敌人碰撞矩形: {self.rect}")

        # 敌人属性
        self.stats = ENEMY_STATS[enemy_type].copy()
        self.hp = self.stats["hp"] + (level - 1) * 10  # 每关血量增加10
        self.speed = self.stats["speed"]
        self.attack_damage = self.stats["attack_damage"]
        self.attack_range = self.stats["attack_range"]
        self.attack_interval = self.stats["attack_interval"]
        self.last_attack = pygame.time.get_ticks()

        # AI状态
        self.state = "patrol"  # patrol, chase, attack
        self.patrol_point = None
        self.patrol_wait = 0
        self.direction = pygame.math.Vector2(0, 0)
        self.facing_right = True

        # 远程攻击的投射物
        self.projectiles = pygame.sprite.Group()

        print(f"{enemy_type} 敌人初始化完成，HP: {self.hp}, 速度: {self.speed}, 攻击力: {self.attack_damage}")

    def update(self):
        """更新敌人状态"""
        # 检查生命值
        if self.hp <= 0:
            self.kill()
            return

        # 更新AI状态
        self.update_ai_state()

        # 根据状态执行行为
        dt = 16  # 默认时间增量
        if self.state == "idle":
            pass  # 闲置状态不移动
        elif self.state == "patrol":
            self.patrol(dt)
        elif self.state == "chase":
            # 在追逐前检查墙体碰撞
            self.check_wall_collisions()
            self.chase_player(dt)
        elif self.state == "attack":
            self.attack_player(dt)

        # 根据移动方向翻转图像
        self.update_facing_direction()

        # 更新投射物
        for projectile in list(self.projectiles):
            projectile.update(dt)

    def update_facing_direction(self):
        """更新敌人朝向"""
        # 根据移动方向翻转图像
        if self.direction.x > 0:
            self.facing_right = True
        elif self.direction.x < 0:
            self.facing_right = False

        # 获取原始图像（未翻转）
        if hasattr(self, 'original_image'):
            original = self.original_image
        else:
            original = self.image
            self.original_image = original.copy()

        # 翻转图像
        if not self.facing_right:
            self.image = pygame.transform.flip(original, True, False)
        else:
            self.image = original.copy()

        # 调试输出
        if hasattr(self, 'debug_timer') and pygame.time.get_ticks() - getattr(self, 'debug_timer', 0) > 5000:
            self.debug_timer = pygame.time.get_ticks()
            print(f"{self.enemy_type} 敌人位置: {self.rect.topleft}, 朝向: {'右' if self.facing_right else '左'}")

    def update_ai_state(self):
        """更新AI状态"""
        # 获取玩家
        if not hasattr(self, 'game') or not self.game or not hasattr(self.game, 'player'):
            return

        player = self.game.player

        # 计算与玩家的距离
        distance = pygame.math.Vector2(self.rect.center).distance_to(
            pygame.math.Vector2(player.rect.center))

        # 检查是否能看到玩家（无障碍物阻挡）
        can_see = self.can_see_player()

        # 根据距离和视线更新状态
        if can_see:
            if distance <= self.attack_range:
                self.state = "attack"
            elif distance <= 400:  # 追逐范围
                self.state = "chase"
            else:
                self.state = "patrol"
        else:
            # 如果看不到玩家，回到巡逻状态
            self.state = "patrol"

        # 调试信息（只在状态变化时输出）
        if not hasattr(self, '_last_state') or self._last_state != self.state:
            print(f"{self.enemy_type}敌人状态变为: {self.state}, 距离: {distance:.1f}, 能看到玩家: {can_see}")
            self._last_state = self.state

    def can_see_player(self):
        """检查是否能看到玩家（无障碍物阻挡）"""
        if not hasattr(self, 'game') or not self.game or not hasattr(self.game, 'player'):
            return False

        player = self.game.player
        start = pygame.math.Vector2(self.rect.center)
        end = pygame.math.Vector2(player.rect.center)
        distance = start.distance_to(end)

        # 如果距离太远，直接返回False
        if distance > 400:  # 视野范围
            return False

        # 检查是否有墙体阻挡
        direction = (end - start)
        if direction.length() > 0:
            direction.normalize_ip()
        else:
            return False

        # 使用射线检测
        step_size = 10  # 每步检测的距离
        steps = int(distance / step_size)

        for i in range(steps):
            point = start + direction * (i * step_size)
            point_rect = pygame.Rect(point.x, point.y, 2, 2)

            if hasattr(self.game, 'level_system') and hasattr(self.game.level_system, 'walls'):
                for wall in self.game.level_system.walls:
                    if wall.colliderect(point_rect):
                        return False

        return True

    def check_wall_collisions(self):
        """检查并处理墙体碰撞"""
        if not hasattr(self, 'game') or not self.game or not hasattr(self.game, 'level_system'):
            return

        # 获取当前位置
        current_pos = pygame.math.Vector2(self.rect.center)

        # 获取到玩家的方向
        player_pos = pygame.math.Vector2(self.game.player.rect.center)
        direction = player_pos - current_pos

        if direction.length() > 0:
            direction.normalize_ip()

        # 检查前方是否有墙
        step_size = 32  # 检测步长
        check_pos = current_pos + direction * step_size
        check_rect = pygame.Rect(check_pos.x - 16, check_pos.y - 16, 32, 32)

        for wall in self.game.level_system.walls:
            if isinstance(wall, pygame.Rect) and wall.colliderect(check_rect):
                # 如果前方有墙，改变状态为巡逻
                self.state = "patrol"
                print(f"敌人检测到前方有墙，改变状态为巡逻")
                return True

        return False

    def patrol(self, dt):
        """巡逻行为，使用时间增量实现平滑动画"""
        current_time = pygame.time.get_ticks()

        # 如果没有巡逻点或已到达巡逻点，设置新的巡逻点
        if self.patrol_point is None or (abs(self.rect.centerx - self.patrol_point[0]) < 10 and
                                         abs(self.rect.centery - self.patrol_point[1]) < 10):
            if current_time > self.patrol_wait:
                # 在附近随机选择一个点
                x = random.randint(max(50, self.rect.centerx - 200),
                                  min(SCREEN_WIDTH - 50, self.rect.centerx + 200))
                y = random.randint(max(50, self.rect.centery - 200),
                                  min(SCREEN_HEIGHT - 50, self.rect.centery + 200))

                # 确保点不在墙内
                valid_point = True
                test_rect = pygame.Rect(x - 16, y - 16, 32, 32)
                for wall in self.game.level_system.walls:
                    if wall.colliderect(test_rect):
                        valid_point = False
                        break

                if valid_point:
                    self.patrol_point = (x, y)
                    self.patrol_wait = current_time + random.randint(2000, 5000)
                else:
                    # 如果找不到有效点，等待一会再试
                    self.patrol_wait = current_time + 1000
                    return
            else:
                # 等待
                return

        # 向巡逻点移动
        if self.patrol_point:
            target = pygame.math.Vector2(self.patrol_point)
            current = pygame.math.Vector2(self.rect.center)
            direction = target - current

            if direction.length() > 0:
                direction.normalize_ip()
                # 使用时间增量调整移动速度
                adjusted_speed = self.speed * 0.5 * (dt / 16)
                self.direction = direction
                self.rect.x += direction.x * adjusted_speed
                self.rect.y += direction.y * adjusted_speed

    def _find_patrol_point(self):
        """查找有效的巡逻点"""
        # 定义搜索区域
        search_radius = 200
        center_x, center_y = self.rect.center

        # 尝试多个随机点
        for _ in range(10):
            # 在附近随机选择一个点
            x = random.randint(max(50, center_x - search_radius),
                              min(SCREEN_WIDTH - 50, center_x + search_radius))
            y = random.randint(max(50, center_y - search_radius),
                              min(SCREEN_HEIGHT - 50, center_y + search_radius))

            # 确保点不在墙内
            valid_point = True
            temp_rect = pygame.Rect(x - 16, y - 16, 32, 32)
            for wall in self.game.level_system.walls:
                if wall.colliderect(temp_rect):
                    valid_point = False
                    break

            if valid_point:
                return (x, y)

        # 如果找不到有效点，返回None
        return None

    def chase_player(self, dt):
        """追逐玩家，改进的寻路算法"""
        if not hasattr(self, 'game') or not self.game or not hasattr(self.game, 'player'):
            return

        player = self.game.player

        # 计算到玩家的直线方向
        target_direction = pygame.math.Vector2(player.rect.center) - pygame.math.Vector2(self.rect.center)
        if target_direction.length() > 0:
            target_direction.normalize_ip()

        # 保存原始位置
        original_x = self.rect.x
        original_y = self.rect.y

        # 计算移动量
        move_x = target_direction.x * self.speed * (dt / 16)
        move_y = target_direction.y * self.speed * (dt / 16)

        # 尝试直线移动
        self.rect.x += move_x
        self.rect.y += move_y

        # 检查是否与墙体碰撞（包括临时墙体）
        wall_collision = False
        if hasattr(self.game, 'level_system') and hasattr(self.game.level_system, 'walls'):
            # 检查永久墙体
            for wall in self.game.level_system.walls:
                if self.rect.colliderect(wall):
                    wall_collision = True
                    break

            # 检查临时墙体（土墙术等）
            if not wall_collision and hasattr(self.game.level_system, 'temporary_walls'):
                for temp_wall in self.game.level_system.temporary_walls:
                    if self.rect.colliderect(temp_wall['rect']):
                        wall_collision = True
                        break

        if wall_collision:
            # 恢复位置
            self.rect.x = original_x
            self.rect.y = original_y

            # 尝试分别在X和Y方向移动
            # 先尝试X方向
            self.rect.x += move_x
            x_collision = False
            # 检查永久墙体
            for wall in self.game.level_system.walls:
                if self.rect.colliderect(wall):
                    x_collision = True
                    break
            # 检查临时墙体
            if not x_collision and hasattr(self.game.level_system, 'temporary_walls'):
                for temp_wall in self.game.level_system.temporary_walls:
                    if self.rect.colliderect(temp_wall['rect']):
                        x_collision = True
                        break

            if x_collision:
                self.rect.x = original_x

            # 再尝试Y方向
            self.rect.y += move_y
            y_collision = False
            # 检查永久墙体
            for wall in self.game.level_system.walls:
                if self.rect.colliderect(wall):
                    y_collision = True
                    break
            # 检查临时墙体
            if not y_collision and hasattr(self.game.level_system, 'temporary_walls'):
                for temp_wall in self.game.level_system.temporary_walls:
                    if self.rect.colliderect(temp_wall['rect']):
                        y_collision = True
                        break

            if y_collision:
                self.rect.y = original_y

            # 如果两个方向都被阻挡，尝试绕路
            if x_collision and y_collision:
                self._try_pathfinding(target_direction, dt)

        # 更新方向
        self.direction = target_direction

    def _try_pathfinding(self, target_direction, dt):
        """简单的绕路算法"""
        # 尝试几个替代方向
        alternative_directions = [
            pygame.math.Vector2(-target_direction.y, target_direction.x),  # 垂直方向1
            pygame.math.Vector2(target_direction.y, -target_direction.x),  # 垂直方向2
            pygame.math.Vector2(-target_direction.x, target_direction.y),  # 反向X
            pygame.math.Vector2(target_direction.x, -target_direction.y),  # 反向Y
        ]

        for alt_dir in alternative_directions:
            if alt_dir.length() > 0:
                alt_dir.normalize_ip()

                # 计算移动量
                move_x = alt_dir.x * self.speed * 0.5 * (dt / 16)  # 绕路时速度减半
                move_y = alt_dir.y * self.speed * 0.5 * (dt / 16)

                # 保存当前位置
                test_x = self.rect.x
                test_y = self.rect.y

                # 尝试移动
                self.rect.x += move_x
                self.rect.y += move_y

                # 检查碰撞
                collision = False
                for wall in self.game.level_system.walls:
                    if self.rect.colliderect(wall):
                        collision = True
                        break

                if not collision:
                    # 找到可行路径，退出
                    return
                else:
                    # 恢复位置，尝试下一个方向
                    self.rect.x = test_x
                    self.rect.y = test_y

    def attack_player(self, dt):
        """攻击玩家"""
        current_time = pygame.time.get_ticks()

        if current_time - self.last_attack > self.attack_interval:
            if self.enemy_type == "melee":
                # 近战攻击
                if pygame.math.Vector2(self.rect.center).distance_to(
                   pygame.math.Vector2(self.game.player.rect.center)) <= self.attack_range:
                    # 使用take_damage方法
                    self.game.player.take_damage(self.attack_damage)
                    self.last_attack = current_time
                    print(f"近战敌人攻击玩家! 造成 {self.attack_damage} 点伤害")
            else:
                # 远程攻击
                self.fire_projectile()
                self.last_attack = current_time

    def move_towards(self, target, speed):
        """向目标点移动"""
        # 安全检查
        if not hasattr(self, 'game') or not self.game or not hasattr(self.game, 'level_system'):
            return

        current = pygame.math.Vector2(self.rect.center)
        self.direction = (target - current)

        if self.direction.length() > 0:
            self.direction = self.direction.normalize()

        # 计算新位置
        new_x = self.rect.x + self.direction.x * speed
        new_y = self.rect.y + self.direction.y * speed

        # 分别检查X和Y方向的移动
        temp_rect_x = self.rect.copy()
        temp_rect_x.x = new_x

        temp_rect_y = self.rect.copy()
        temp_rect_y.y = new_y

        # 检查墙体碰撞
        can_move_x = True
        can_move_y = True

        # 检查边界碰撞
        if new_x < 0 or new_x + self.rect.width > SCREEN_WIDTH:
            can_move_x = False
            print(f"敌人碰到边界: X={new_x}")
        if new_y < 0 or new_y + self.rect.height > SCREEN_HEIGHT:
            can_move_y = False
            print(f"敌人碰到边界: Y={new_y}")

        # 打印墙体信息，帮助调试
        print(f"墙体数量: {len(self.game.level_system.walls)}")

        # 检查墙体碰撞
        for wall in self.game.level_system.walls:
            # 确保wall是Rect对象
            if not isinstance(wall, pygame.Rect):
                try:
                    wall_rect = pygame.Rect(wall)
                except:
                    continue
            else:
                wall_rect = wall

            if temp_rect_x.colliderect(wall_rect):
                can_move_x = False
                print(f"敌人碰到墙体: X方向, 墙体={wall_rect}")
            if temp_rect_y.colliderect(wall_rect):
                can_move_y = False
                print(f"敌人碰到墙体: Y方向, 墙体={wall_rect}")

        # 应用有效的移动
        if can_move_x:
            self.rect.x = new_x
        if can_move_y:
            self.rect.y = new_y

    def fire_projectile(self):
        """发射投射物"""
        if not hasattr(self.game, 'level_system'):
            print("警告: 无法访问level_system，无法发射投射物")
            return

        # 获取玩家位置
        player_pos = self.game.player.rect.center
        start_pos = self.rect.center

        # 创建投射物（统一使用fire_ball.png图像，传递敌人朝向）
        # 获取敌人朝向，尝试多种属性名
        enemy_direction = getattr(self, 'facing_direction',
                                getattr(self, 'direction',
                                       getattr(self, 'face_direction', 'right')))

        print(f"远程敌人准备攻击，敌人朝向: {enemy_direction}")
        projectile = Projectile(start_pos, player_pos, self.attack_damage, "fire", enemy_direction)

        # 设置游戏引用
        projectile.game = self.game

        # 添加到游戏中
        self.game.level_system.add_projectile(projectile)
        print(f"{self.enemy_type} 敌人发射了火球投射物（fire_ball.png）")

    def take_damage(self, damage):
        """敌人受到伤害"""
        self.hp -= damage

        # 移除受伤特效
        # if hasattr(self, 'game') and hasattr(self.game, 'combat_system'):
        #     self.game.combat_system.add_damage_effects(self.rect.center, damage)

        print(f"{self.enemy_type} 敌人受到 {damage} 点伤害，剩余HP: {self.hp}")

        # 检查是否死亡
        if self.hp <= 0:
            self.hp = 0
            print(f"{self.enemy_type} 敌人死亡")
            # 可以在这里添加死亡逻辑

class Projectile(pygame.sprite.Sprite):
    def __init__(self, start_pos, target_pos, damage, spell_type, enemy_direction="right"):
        super().__init__()
        self.spell_type = spell_type
        self.enemy_direction = enemy_direction  # 敌人的朝向

        # 所有远程攻击都使用fire_ball.png图像
        try:
            # 加载fire_ball.png图像
            self.image = pygame.image.load("assets/skills/effects/fire_ball.png").convert_alpha()
            # 应用黑色透明处理
            self.image.set_colorkey((0, 0, 0))
            # 等比例缩小到原始尺寸的60%（134x50 -> 80x30）
            original_size = self.image.get_size()
            new_width = int(original_size[0] * 0.6)
            new_height = int(original_size[1] * 0.6)
            self.image = pygame.transform.scale(self.image, (new_width, new_height))
            print(f"成功加载火球图像，原始尺寸: {original_size}, 缩放后: {new_width}x{new_height}")
        except Exception as e:
            print(f"加载火球图像失败: {e}，使用默认图像")
            # 备用图像（调整为相应尺寸）
            self.image = pygame.Surface((30, 30), pygame.SRCALPHA)
            pygame.draw.circle(self.image, (255, 100, 0), (15, 15), 15)
            pygame.draw.circle(self.image, (255, 200, 0), (15, 15), 10)
            pygame.draw.circle(self.image, (255, 255, 100), (15, 15), 5)

        self.rect = self.image.get_rect(center=start_pos)
        self.position = pygame.math.Vector2(start_pos)
        self.damage = damage

        # 先计算方向和速度，确定最终的敌人朝向
        direction = pygame.math.Vector2(target_pos) - self.position
        if direction.length() > 0:
            self.velocity = direction.normalize() * 6  # 提高速度以获得更好的视觉效果
            # 根据飞行方向智能判断火球朝向（如果敌人朝向不明确）
            if self.enemy_direction not in ['left', 'right']:
                if direction.x < 0:
                    self.enemy_direction = 'left'  # 向左飞行
                else:
                    self.enemy_direction = 'right'  # 向右飞行
                print(f"根据飞行方向智能判断火球朝向: {self.enemy_direction}")
        else:
            self.velocity = pygame.math.Vector2(0, 0)

        # 在确定最终朝向后，调整火球方向
        self.apply_direction_transform()

        # 保存经过方向调整后的图像用于渐隐效果
        self.original_image = self.image.copy()

        # 投射物生命周期和渐隐设置
        self.creation_time = pygame.time.get_ticks()
        self.lifetime = 2500  # 2.5秒后消失
        self.max_distance = 500  # 最大飞行距离
        self.fade_start_distance = 300  # 开始渐隐的距离
        self.start_pos = pygame.math.Vector2(start_pos)
        self.game = None  # 将在fire_projectile中设置

        print(f"创建火球投射物，起点: {start_pos}, 目标: {target_pos}, 伤害: {damage}, 敌人朝向: {enemy_direction}")

    def apply_direction_transform(self):
        """根据敌人朝向调整火球方向"""
        print(f"开始处理火球方向，敌人朝向: {self.enemy_direction}")

        # fire_ball.png默认是朝右的图像
        # 如果敌人朝左，需要翻转火球使其朝左
        if self.enemy_direction == "left":
            # 水平翻转火球，使其朝左
            self.image = pygame.transform.flip(self.image, True, False)
            print(f"敌人朝左，火球已翻转为左向")
        elif self.enemy_direction == "right":
            # 保持原始方向（朝右）
            print(f"敌人朝右，火球保持原始方向（朝右）")
        else:
            # 默认情况，保持原始方向
            print(f"未知敌人朝向: {self.enemy_direction}，保持原始方向")

    def update(self, dt=16):
        """更新投射物位置和渐隐效果"""
        # 更新位置
        self.position += self.velocity
        self.rect.center = (int(self.position.x), int(self.position.y))

        # 计算飞行距离
        distance_traveled = self.position.distance_to(self.start_pos)

        # 检查是否超过最大飞行距离
        if distance_traveled > self.max_distance:
            print(f"投射物超过最大距离{self.max_distance}，消失")
            self.kill()
            return

        # 检查生命周期
        current_time = pygame.time.get_ticks()
        if current_time - self.creation_time > self.lifetime:
            print(f"投射物超过生命周期{self.lifetime}ms，消失")
            self.kill()
            return

        # 渐隐效果（当飞行距离超过渐隐起始距离时开始）
        if distance_traveled > self.fade_start_distance:
            # 计算渐隐系数（0到1）
            fade_distance = distance_traveled - self.fade_start_distance
            max_fade_distance = self.max_distance - self.fade_start_distance
            fade_ratio = min(fade_distance / max_fade_distance, 1.0)

            # 计算透明度（从255逐渐减少到0）
            alpha = int(255 * (1.0 - fade_ratio))

            # 创建渐隐后的图像（保持方向）
            faded_image = self.original_image.copy()
            faded_image.set_alpha(alpha)
            self.image = faded_image

        # 检查墙体碰撞
        if hasattr(self, 'game') and self.game and hasattr(self.game, 'level_system'):
            for wall in self.game.level_system.walls:
                if self.rect.colliderect(wall):
                    print(f"火球投射物击中墙体")
                    self.kill()
                    return

            # 检查玩家碰撞
            if self.rect.colliderect(self.game.player.rect):
                # 使用take_damage方法
                print(f"火球投射物击中玩家! 造成 {self.damage} 点伤害")
                self.game.player.take_damage(self.damage)
                self.kill()
                return
