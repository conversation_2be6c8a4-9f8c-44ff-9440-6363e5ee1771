#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查找远程敌人攻击方法
"""

def find_ranged_attack_method():
    """查找远程敌人攻击方法"""
    try:
        with open('enemies.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print("搜索远程敌人攻击方法...")
        
        for i, line in enumerate(lines, 1):
            if 'fire_projectile' in line or '发射投射物' in line:
                print(f"第{i}行: {line.strip()}")
                
                # 显示周围的代码
                start = max(0, i-5)
                end = min(len(lines), i+10)
                print(f"\n周围代码 (第{start+1}-{end}行):")
                for j in range(start, end):
                    marker = ">>> " if j == i-1 else "    "
                    print(f"{marker}{j+1:4d}: {lines[j].rstrip()}")
                print()
        
    except Exception as e:
        print(f"搜索失败: {e}")

if __name__ == "__main__":
    find_ranged_attack_method()
