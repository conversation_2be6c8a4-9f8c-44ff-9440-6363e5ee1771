#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
玩家死亡界面
"""

import pygame
from config import SCREEN_WIDTH, SCREEN_HEIGHT

class DeathScreen:
    def __init__(self):
        """初始化死亡界面"""
        self.font_large = pygame.font.Font(None, 72)
        self.font_medium = pygame.font.Font(None, 48)
        
        # 死亡界面背景图片路径
        self.death_bg_path = "assets/ui/death_background.png"
        
        # 重新开始按钮图片路径
        self.restart_button_path = "assets/ui/restart_button.png"
        
        # 加载图片
        self.load_images()
        
        # 按钮位置和状态
        self.restart_button_rect = None
        self.setup_buttons()
        
        print("死亡界面初始化完成")
        print(f"死亡背景图片路径: {self.death_bg_path}")
        print(f"重新开始按钮图片路径: {self.restart_button_path}")
    
    def load_images(self):
        """加载界面图片"""
        try:
            # 加载死亡背景
            self.death_bg = pygame.image.load(self.death_bg_path).convert_alpha()
            self.death_bg = pygame.transform.scale(self.death_bg, (SCREEN_WIDTH, SCREEN_HEIGHT))
            print(f"✅ 成功加载死亡背景: {self.death_bg_path}")
        except Exception as e:
            print(f"⚠️ 加载死亡背景失败: {e}")
            # 创建默认背景
            self.death_bg = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
            self.death_bg.fill((50, 0, 0))  # 深红色背景
            # 添加半透明覆盖层
            overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
            overlay.set_alpha(128)
            overlay.fill((0, 0, 0))
            self.death_bg.blit(overlay, (0, 0))
        
        try:
            # 加载重新开始按钮
            self.restart_button_img = pygame.image.load(self.restart_button_path).convert_alpha()
            # 调整按钮大小
            button_width, button_height = 200, 60
            self.restart_button_img = pygame.transform.scale(self.restart_button_img, (button_width, button_height))
            print(f"✅ 成功加载重新开始按钮: {self.restart_button_path}")
        except Exception as e:
            print(f"⚠️ 加载重新开始按钮失败: {e}")
            # 创建默认按钮
            button_width, button_height = 200, 60
            self.restart_button_img = pygame.Surface((button_width, button_height))
            self.restart_button_img.fill((100, 100, 100))
            pygame.draw.rect(self.restart_button_img, (200, 200, 200), 
                           (0, 0, button_width, button_height), 3)
            
            # 添加按钮文字
            font = pygame.font.Font(None, 36)
            text = font.render("RESTART", True, (255, 255, 255))
            text_rect = text.get_rect(center=(button_width//2, button_height//2))
            self.restart_button_img.blit(text, text_rect)
    
    def setup_buttons(self):
        """设置按钮位置"""
        # 重新开始按钮位置（屏幕中央偏下）
        button_x = SCREEN_WIDTH // 2 - self.restart_button_img.get_width() // 2
        button_y = SCREEN_HEIGHT // 2 + 100
        self.restart_button_rect = pygame.Rect(
            button_x, button_y,
            self.restart_button_img.get_width(),
            self.restart_button_img.get_height()
        )
        
        print(f"重新开始按钮位置: {self.restart_button_rect}")
    
    def handle_event(self, event):
        """处理事件"""
        if event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # 左键点击
                mouse_pos = pygame.mouse.get_pos()
                
                # 检查重新开始按钮点击
                if self.restart_button_rect.collidepoint(mouse_pos):
                    print("🔄 点击重新开始按钮")
                    return "RESTART"
        
        return None
    
    def draw(self, screen):
        """绘制死亡界面"""
        # 绘制背景
        screen.blit(self.death_bg, (0, 0))
        
        # 绘制死亡标题
        death_text = self.font_large.render("GAME OVER", True, (255, 50, 50))
        death_rect = death_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 - 100))
        screen.blit(death_text, death_rect)
        
        # 绘制副标题
        subtitle_text = self.font_medium.render("You have been defeated...", True, (255, 255, 255))
        subtitle_rect = subtitle_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 - 50))
        screen.blit(subtitle_text, subtitle_rect)
        
        # 绘制重新开始按钮
        screen.blit(self.restart_button_img, self.restart_button_rect)
        
        # 绘制按钮悬停效果
        mouse_pos = pygame.mouse.get_pos()
        if self.restart_button_rect.collidepoint(mouse_pos):
            # 绘制悬停边框
            pygame.draw.rect(screen, (255, 255, 255), self.restart_button_rect, 3)
    
    def get_required_assets(self):
        """返回需要的资源文件路径列表"""
        return [
            {
                "path": self.death_bg_path,
                "description": "死亡界面背景图片",
                "recommended_size": f"{SCREEN_WIDTH}x{SCREEN_HEIGHT}",
                "format": "PNG (支持透明度)"
            },
            {
                "path": self.restart_button_path,
                "description": "重新开始按钮图片",
                "recommended_size": "200x60",
                "format": "PNG (支持透明度)"
            }
        ]
